"use client"

import React, { useRef } from "react"

import { useGSAP } from "@gsap/react"
import gsap from "gsap"

type Props = {
  children: React.ReactNode
}

const AnimatedButton = ({ children }: Props) => {
  const ref = useRef<HTMLButtonElement>(null)

  useGSAP(
    () => {
      gsap.from(ref.current, {
        opacity: 0,
        y: 20,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ref.current,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      })
    },
    { scope: ref },
  )

  return (
    <button ref={ref} className="rounded bg-blue-600 px-6 py-3 text-white">
      {children}
    </button>
  )
}

export default AnimatedButton
