import { Children, cloneElement, isValidElement } from "react"

import useMediaQuery from "@/hooks/use-media-query"

type TProps = {
  children: React.ReactNode
}

const GsapDisableAnimationOnMobile = ({ children }: TProps) => {
  const isMobile = useMediaQuery("(max-width: 768px)")

  if (!isMobile) return children

  const modifiedChildren = Children.map(children, (child) => {
    if (isValidElement(child)) {
      const emptyAnimationProps = {
        animation: "default",
        initial: undefined,
        exit: undefined,
        transition: { duration: 0 },
        variants: {
          default: {
            opacity: 0,
            top: 0,
            bottom: 0,
          },
        },
      }

      return cloneElement(child, {
        ...emptyAnimationProps,
      })
    }
    return child
  })

  return modifiedChildren
}

export default GsapDisableAnimationOnMobile
