"use client"

import { useRef } from "react"
import { useScrambleText } from "../hooks/use-scramble-text"
import { useScrollTo } from "../hooks/use-scroll-to"

type Props = {
  text: string
  options?: {
    delay?: number
    scrollTrigger?: boolean
  }
}

export default function GsapScrambled({
  text = "Scrambled Headline ✨",
  options,
}: Props) {
  const ref = useRef<HTMLElement>(null)
  const scrollTo = useScrollTo()

  useScrambleText({
    ref: ref as React.RefObject<HTMLElement>,
    text,
    delay: options?.delay,
    scrollTrigger: options?.scrollTrigger,
  })

  return (
    <div className="flex h-80 w-screen flex-col items-center justify-center">
      <h1
        ref={ref as React.RefObject<HTMLHeadingElement>}
        className="text-4xl font-bold"
      />
      <button onClick={() => scrollTo("#section2")}>Scroll to Section</button>
    </div>
  )
}
