"use client"

import { useRef } from "react"
import { useScrollSmoother } from "../hooks/use-scroll-smoother"

type Props = {
  children: React.ReactNode
  gsapOptions?: Record<string, any>
}

const GsapScrollSmoother = ({ children, gsapOptions }: Props) => {
  const wrapperRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  useScrollSmoother({
    wrapperRef: wrapperRef as React.RefObject<HTMLElement>,
    contentRef: contentRef as React.RefObject<HTMLElement>,
    smooth: 1,
    effects: true,
    ...gsapOptions,
  })

  return (
    <div
      data-component="gsap-scroll-smoother"
      ref={wrapperRef}
      style={{
        boxSizing: "border-box",
        border: "none",
        outline: "none",
        transform: "translateZ(0)",
        willChange: "transform",
        backfaceVisibility: "hidden",
        WebkitFontSmoothing: "antialiased",
        MozOsxFontSmoothing: "grayscale",
      }}
    >
      <div
        data-component="gsap-scroll-smoother-content"
        ref={contentRef}
        style={{
          boxSizing: "border-box",
          border: "none",
          outline: "none",
          transform: "translateZ(0)",
          willChange: "transform",
          backfaceVisibility: "hidden",
          WebkitFontSmoothing: "antialiased",
          MozOsxFontSmoothing: "grayscale",
        }}
      >
        {children}
      </div>
    </div>
  )
}

export default GsapScrollSmoother
