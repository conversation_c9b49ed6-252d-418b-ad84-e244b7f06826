// components/SplitHeading.tsx
"use client"

import { useRef } from "react"
import { useSplitTextAnimation } from "../hooks/use-split-text"

export default function GsapTextAnimate({ text }: { text: string }) {
  const headingRef = useRef<HTMLHeadingElement>(null)

  useSplitTextAnimation({
    ref: headingRef as React.RefObject<HTMLElement>,
    type: "chars,words,lines",
    lineClass: "line",
    scrollTrigger: true,
  })

  return (
    <div className="my-10">
      <h1 ref={headingRef}>{text}</h1>
    </div>
  )
}
