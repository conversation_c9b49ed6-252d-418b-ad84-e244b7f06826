"use client"

import { useRef } from "react"

import { useGSAP } from "@gsap/react"
import gsap from "gsap"

type Props = {
  text: string
  options?: Record<string, any>
}

const Heading = ({ text, options }: Props) => {
  const ref = useRef<HTMLHeadingElement>(null)

  useGSAP(
    () => {
      gsap.from(ref.current, {
        opacity: 0,
        y: 20,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ref.current,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      })
    },
    { scope: ref },
  )

  return (
    <>
      <h2 ref={ref} className="mb-6 text-4xl font-bold">
        {text}
      </h2>
    </>
  )
}

export default Heading
