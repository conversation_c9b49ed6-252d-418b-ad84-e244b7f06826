"use client"

import { cn } from "@/utils"
import React, { useRef } from "react"

import { useGSAP } from "@gsap/react"
import gsap from "gsap"

type Props = {
  children: React.ReactNode
  className?: string
}

const Section = ({ children, className }: Props) => {
  const ref = useRef<HTMLElement | null>(null)

  useGSAP(
    () => {
      gsap.from(ref.current, {
        opacity: 0,
        y: 20,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: ref.current,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      })
    },
    { scope: ref },
  )

  return (
    <section ref={ref} className={cn("bg-gray-100 px-6 py-20", className)}>
      {children}
    </section>
  )
}

export default Section
