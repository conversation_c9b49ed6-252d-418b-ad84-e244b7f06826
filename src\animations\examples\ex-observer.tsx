export default function ExObserver() {
  const h2Classes =
    "text-[clamp(1rem,5vw,5rem)] font-normal text-center tracking-[0.5em] -mr-[0.5em] text-[hsl(0,0%,80%)] w-[90vw] max-w-[1200px]"

  return (
    <div className="min-h-screen w-screen">
      <h1 className="text-4xl font-bold">ExObserver</h1>

      <section className="first">
        <div className="outer">
          <div className="inner">
            <div className="bg one">
              <h2 className={h2Classes}>Scroll down</h2>
            </div>
          </div>
        </div>
      </section>
      <section className="second">
        <div className="outer">
          <div className="inner">
            <div className="bg">
              <h2 className={h2Classes}>Animated with GSAP</h2>
            </div>
          </div>
        </div>
      </section>
      <section className="third">
        <div className="outer">
          <div className="inner">
            <div className="bg">
              <h2 className={h2Classes}>GreenSock</h2>
            </div>
          </div>
        </div>
      </section>
      <section className="fourth">
        <div className="outer">
          <div className="inner">
            <div className="bg">
              <h2 className={h2Classes}>Animation platform</h2>
            </div>
          </div>
        </div>
      </section>
      <section className="fifth">
        <div className="outer">
          <div className="inner">
            <div className="bg">
              <h2 className={h2Classes}>Keep scrolling</h2>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
