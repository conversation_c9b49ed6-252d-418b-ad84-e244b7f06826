"use client"

import { <PERSON><PERSON> } from "@/components/ui"
import { useScrollTo } from "../hooks/use-scroll-to"

import { GsapScrollSmoother } from "../components"

export default function ExScrollSmoother() {
  const scrollTo = useScrollTo()

  return (
    <GsapScrollSmoother>
      <div className="flex flex-col justify-between bg-black/50 p-8">
        <h1 className="text-center text-4xl font-bold">ScrollSmoother</h1>
        <div className="flex justify-center gap-4 p-4">
          <Button onClick={() => scrollTo(".box-c")}>Jump to C</Button>
          <Button onClick={() => scrollTo(".box-e")}>Jump to E</Button>
        </div>
      </div>

      <div className="box box-a flex h-[300px] flex-col items-center justify-center bg-red-500">
        <h2 className="text-4xl font-bold">A</h2>
      </div>
      <div className="box box-b mt-20 flex h-[300px] flex-col items-center justify-center bg-blue-600">
        <h2 className="text-4xl font-bold">B</h2>
      </div>
      <div className="box box-c mt-20 flex h-[300px] flex-col items-center justify-center bg-green-600">
        <h2 className="text-4xl font-bold">C</h2>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam,
          quos.
        </p>
      </div>
      <div className="box box-d mt-20 flex h-[300px] flex-col items-center justify-center bg-yellow-600">
        <h2 className="text-4xl font-bold">D</h2>
        <p>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam,
          quos.
        </p>
        <Button onClick={() => scrollTo(".box-a")}>Jump to A</Button>
      </div>
      <div className="box box-e mt-20 flex h-[300px] flex-col items-center justify-center bg-purple-600">
        <h2 className="text-4xl font-bold">E</h2>
      </div>
    </GsapScrollSmoother>
  )
}
