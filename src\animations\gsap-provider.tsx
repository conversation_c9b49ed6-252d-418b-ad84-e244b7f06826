// src/animations/gsap-provider.tsx

"use client"

import { useEffect } from "react"

import { useGSAP } from "@gsap/react"
import { gsap } from "gsap"

// plugins
import Flip from "gsap/Flip"
import MotionPathPlugin from "gsap/MotionPathPlugin"
import Observer from "gsap/Observer"
import ScrambleTextPlugin from "gsap/ScrambleTextPlugin"
import ScrollSmoother from "gsap/ScrollSmoother"
import ScrollToPlugin from "gsap/ScrollToPlugin"
import ScrollTrigger from "gsap/ScrollTrigger"
import SplitText from "gsap/SplitText"
import TextPlugin from "gsap/TextPlugin"

export const GSAPProvider = ({ children }: { children: React.ReactNode }) => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      gsap.registerPlugin(
        useGSAP,
        SplitText,
        ScrollTrigger,
        ScrollSmoother,
        Observer,
        ScrollToPlugin,
        ScrambleTextPlugin,
        TextPlugin,
        Flip,
        MotionPathPlugin,
      )
    }
  }, [])

  return children
}
