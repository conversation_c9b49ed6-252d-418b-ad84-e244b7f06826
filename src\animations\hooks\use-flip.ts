// src/animations/hooks/use-flip.ts

"use client"

import { Flip } from "gsap/Flip"
import { useEffect } from "react"

type TProps = {
  elements: Element[]
  state: Flip.FlipState
  animation?: gsap.TweenVars
}

export const useFlip = ({ elements, state, animation = {} }: TProps) => {
  useEffect(() => {
    Flip.from(state, {
      duration: 0.7,
      ease: "power1.inOut",
      absolute: true,
      ...animation,
    } as Flip.FromToVars)
  }, [elements, state, animation])
}
