// src/animations/hooks/use-observer.ts

"use client"

import Observer from "gsap/Observer"
import { useEffect } from "react"

type TProps = {
  target: string | Element
  onDown?: () => void
  onUp?: () => void
}

export const useObserver = ({ target, onDown, onUp }: TProps) => {
  useEffect(() => {
    const observer = Observer.create({
      target,
      type: "wheel,touch,scroll",
      onDown,
      onUp,
    })

    return () => {
      observer.kill()
    }
  }, [target, onDown, onUp])
}
