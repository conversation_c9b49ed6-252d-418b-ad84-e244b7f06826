// src/animations/hooks/use-scramble-text.ts

"use client"

import { gsap } from "gsap"

import { useGS<PERSON> } from "@gsap/react"

type Props = {
  ref: React.RefObject<HTMLElement>
  text: string
  delay?: number
  scrollTrigger?: boolean
  options?: gsap.TweenVars
}

export const useScrambleText = ({
  ref,
  text,
  delay = 0,
  scrollTrigger = true,
  options = {},
}: Props) => {
  useGSAP(() => {
    gsap.to(ref.current, {
      duration: 2,
      scrambleText: { text },
      delay,
      ease: "none",
      ...options,
      ...(scrollTrigger
        ? {
            scrollTrigger: {
              trigger: ref.current,
              start: "top 80%",
              toggleActions: "play none none reverse",
              ...(options?.scrollTrigger || {}),
            },
          }
        : {}),
    })
  })
}
