// src/animations/hooks/use-scroll-smoother.ts

"use client"

import Scroll<PERSON>moother from "gsap/ScrollSmoother"
import { useRef } from "react"

import { useGSAP } from "@gsap/react"

type TProps = {
  wrapperRef: React.RefObject<HTMLElement>
  contentRef: React.RefObject<HTMLElement>
  smooth?: number
  smoothTouch?: number
  effects?: boolean
  normalizeScroll?: boolean
}

export const useScrollSmoother = ({
  wrapperRef,
  contentRef,
  smooth = 2,
  smoothTouch = 0.1,
  effects = true,
  normalizeScroll = false,
}: TProps) => {
  const smootherRef = useRef<any | null>({ current: null })

  useGSAP(
    () => {
      if (!wrapperRef?.current || !contentRef?.current) return

      smootherRef.current = ScrollSmoother.create({
        wrapper: wrapperRef.current,
        content: contentRef.current,
        smooth,
        smoothTouch,
        effects,
        normalizeScroll,
      })
      return () => {
        smootherRef?.current?.kill()
      }
    },
    {
      scope: wrapperRef,
      dependencies: [smooth, effects, normalizeScroll],
    },
  )

  return smootherRef
}
