// src/animations/hooks/use-scroll-trigger.ts

"use client"

import { useGSAP } from "@gsap/react"
import { gsap } from "gsap"

type TProps = {
  trigger: Element | string
  animation: gsap.TweenVars
  start?: string
  end?: string
  toggleActions?: string
  scrub?: boolean | number
}

export const useScrollTrigger = ({
  trigger,
  animation,
  start = "top bottom",
  end,
  toggleActions = "play none none none",
  scrub = false,
}: TProps) => {
  useGSAP(() => {
    gsap.fromTo(trigger, animation.from, {
      ...animation.to,
      scrollTrigger: {
        trigger,
        start,
        end,
        toggleActions,
        scrub,
      },
    })
  }, [trigger, animation, start, end, toggleActions, scrub])
}
