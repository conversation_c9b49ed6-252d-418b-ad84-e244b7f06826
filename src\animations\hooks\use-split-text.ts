"use client"

// hooks/use-split-text.ts
import { useGSAP } from "@gsap/react"
import { gsap } from "gsap"
import SplitText from "gsap/SplitText"
import { useRef } from "react"

type TProps = {
  ref: React.RefObject<HTMLElement>
  type?: string
  lineClass?: string
  animationProps?: gsap.TweenVars
  scrollTrigger?: boolean
}

export function useSplitTextAnimation({
  ref,
  type = "chars,words",
  lineClass = "line",
  animationProps = {},
  scrollTrigger = true,
}: TProps) {
  const splitRef = useRef<any>(null)

  useGSAP(
    () => {
      if (!ref.current) return

      splitRef.current = new SplitText(ref.current, {
        type,
        linesClass: lineClass,
      })

      const elements = splitRef.current.chars ?? []

      gsap.from(elements, {
        opacity: 0,
        y: 20,
        stagger: 0.05,
        ease: "power2.out",
        duration: 0.8,
        ...animationProps,
        ...(scrollTrigger
          ? {
              scrollTrigger: {
                trigger: ref.current,
                start: "top 80%",
                toggleActions: "play none none reverse",
              },
            }
          : {}),
      })

      return () => {
        splitRef.current?.revert()
      }
    },
    { scope: ref },
  )
}
