// src/animations/hooks/use-text.ts

"use client"

import { gsap } from "gsap"
import { useEffect } from "react"

type TProps = {
  ref: React.RefObject<HTMLElement>
  text: string
  options?: gsap.TweenVars
  scrollTrigger?: boolean
}

export const useText = ({
  ref,
  text,
  options,
  scrollTrigger = true,
}: TProps) => {
  useEffect(() => {
    if (ref.current) {
      gsap.to(ref.current, {
        duration: 1,
        text,
        ease: "power1.inOut",
        ...options,
        ...(scrollTrigger
          ? {
              scrollTrigger: {
                trigger: ref.current,
                start: "top 80%",
                toggleActions: "play none none reverse",
                ...(options?.scrollTrigger || {}),
              },
            }
          : {}),
      })
    }
  }, [ref, text, options, scrollTrigger])
}
