import LoadingPage from "@/components/loading/loading-screen"
import { Metada<PERSON> } from "next"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Bio - Custom Subdomain",
}

type Props = {
  params: { domain: string; handle: string[] }
}

export default async function PageBioDetails({ params }: Props) {
  const { domain, handle } = await params

  const domainDecoded = decodeURIComponent(domain)

  const handlesDecoded = handle.map((h) => decodeURIComponent(h))

  return (
    <Suspense fallback={<LoadingPage />}>
      <div className="flex h-svh flex-col items-center justify-center gap-4 bg-blue-50 p-4">
        <h1 className="text-2xl font-bold">Hello</h1>
        <p className="text-center">
          This is the page for subdomain:
          <br />
          <strong className="italic">{domainDecoded}</strong>
          <br />
          and handle:
          <br />
          <strong className="italic">{handlesDecoded?.join("/")}</strong>
        </p>
      </div>
    </Suspense>
  )
}

export const dynamic = "force-dynamic"
