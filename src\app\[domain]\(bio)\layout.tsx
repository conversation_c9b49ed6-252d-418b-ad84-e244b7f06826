"use client"

import "styles/globals.css"

import PlausibleProvider from "next-plausible"

import { GsapScrollSmoother } from "@/animations/components"
import { plausible } from "@/config-global"
import FooterCopyRight from "@/modules/site-builder/components/preview/footer/copyright"

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <PlausibleProvider
        domain={plausible.domain}
        customDomain={plausible.customHost}
        selfHosted
        trackOutboundLinks
        trackFileDownloads
        enabled={plausible.domain.indexOf("localhost") !== -1 || undefined}
        trackLocalhost={plausible.domain.indexOf("localhost") !== -1}
      />
      <GsapScrollSmoother>
        <div className="mx-auto flex min-h-svh max-w-xl flex-col gap-4">
          {children}
        </div>
      </GsapScrollSmoother>
      <FooterCopyRight />
    </>
  )
}
