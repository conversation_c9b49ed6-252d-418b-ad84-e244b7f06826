import { getPublishedSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"
import { SiteLiveView } from "@/modules/site-live-view"
import { I_WebsiteResponse } from "@/types/app/website"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Website Builder | Crefion",
  description: "Website Builder | Crefion",
}

type TProps = {
  params: Promise<{
    domain: string
    path: string[]
    id: string
  }>
}

export default async function WebsiteLiveView({ params }: TProps) {
  const { id, path, domain } = await params
  // const website = await getSiteDetail(id)

  if (domain === "platform") {
    notFound()
  }

  const pathname = path.join("/")

  const handle = pathname.split("/").pop()

  const website = await getPublishedSiteDetail(id, handle || "/")

  !website && notFound()

  return (
    <SiteLiveView
      website={website as I_WebsiteResponse}
      pagePath={pathname}
      isLiveView
    />
  )
}
