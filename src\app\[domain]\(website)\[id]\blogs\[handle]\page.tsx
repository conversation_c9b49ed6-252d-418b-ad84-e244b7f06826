import { getSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"
import { getBlogDetail } from "@/lib/appwrite-actions/store/blogs"
import { SiteLiveViewBlogDetails } from "@/modules/site-live-view"
import { T_BlogDocument } from "@/types/app/blog"
import { I_WebsiteResponse } from "@/types/app/website"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Blogs | Crefion",
  description: "Blogs | Crefion",
}

type TProps = {
  params: {
    handle: string
    id: string
  }
}

export default async function WebsiteBlogsPage({ params }: TProps) {
  const { id, handle } = await params
  const website = await getSiteDetail(id)

  !website && notFound()

  const details = await getBlogDetail(handle)

  return (
    <SiteLiveViewBlogDetails
      website={website as I_WebsiteResponse}
      handle={handle}
      details={details as T_BlogDocument}
    />
  )
}
