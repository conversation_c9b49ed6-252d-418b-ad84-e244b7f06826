import { Metadata } from "next"
import { notFound } from "next/navigation"

import { I_WebsiteResponse } from "@/types/app/website"

import { getPublishedSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"

import { SiteLiveView } from "@/modules/site-live-view"

export const metadata: Metadata = {
  title: "Website Builder | Crefion",
  description: "Website Builder | Crefion",
}

type TProps = {
  params: Promise<{
    domain: string
    id: string
  }>
  searchParams: Promise<{
    pageId: string
  }>
}

/*
  THIS PAGE IS USED FOR SUBDOMAIN WEBSITE
*/

export default async function WebsiteLiveView({ params }: TProps) {
  const { id } = await params

  const website = await getPublishedSiteDetail(id, "home")
  if (!website) return notFound()

  // const storeProvidersBySiteId = await getStoreProviderBySiteId(id)

  // const cartDataMedusa = await retrieveCartMedusaAPI({
  //   store_url: storeProvidersBySiteId?.store_url || "",
  //   api_key: storeProvidersBySiteId?.api_key || "",
  // })

  return (
    <SiteLiveView
      website={website as I_WebsiteResponse}
      pagePath={"home"}
      // storeProviders={storeProvidersBySiteId ? [storeProvidersBySiteId] : []}
      // cartDataMedusa={cartDataMedusa}
    />
  )
}
