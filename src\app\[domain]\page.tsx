import {
  getAllSites,
  getSiteData,
} from "@/lib/appwrite-actions/admin/domain.action"
// import BioLinkTemplate from "@/modules/bio-link/templates"

type Props = {
  params: { domain: string; handle: string }
}

export async function generateMetadata({ params }: Props) {
  const { domain } = await params
  const domainDecoded = decodeURIComponent(domain)
  const siteData = await getSiteData(domainDecoded)
  if (siteData)
    return {
      title: `${siteData.user.username} - Link in Bio - Creator Tools | Crefion `,
    }
  return {
    title: `Crefion | Creator Tools`,
  }
}
export async function generateStaticParams() {
  const allSites = await getAllSites("BIO")
  const allPaths = allSites
    .flatMap((site: any) => [
      site.subdomain && {
        domain: `${site.subdomain}`,
      },
      site.custom_domain && {
        domain: site.custom_domain,
      },
    ])
    .filter(Boolean)

  return allPaths
}

export default async function PageBioDetails({ params }: Props) {
  const { domain } = await params
  const domainDecoded = decodeURIComponent(domain)
  const siteData = await getSiteData(domainDecoded)

  return (
    <div>
      Page: {domainDecoded}
      <div className="my-4 flex w-[500px] flex-col gap-4 text-wrap break-words">
        {JSON.stringify(siteData?.site)}
      </div>
    </div>
  )

  // return <BioLinkTemplate siteData={siteData} />
}

export const dynamic = "force-dynamic"
