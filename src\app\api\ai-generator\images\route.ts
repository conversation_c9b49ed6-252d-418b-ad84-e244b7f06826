// import { GoogleGenerativeAI } from "@google/generative-ai"
// import { GOOGLE_API } from "@/config-global"
import { NextResponse } from "next/server"

// Configuration for the AI model
// const AI_CONFIG = {
//   temperature: 0.7,
//   maxOutputTokens: 100,
// }

export async function POST(req: Request) {}

export async function GET(req: Request) {
  return NextResponse.json({ message: "Hello, world!" })
}
