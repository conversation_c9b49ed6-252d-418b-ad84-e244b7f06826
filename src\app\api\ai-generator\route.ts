import { GoogleGenerativeAI } from "@google/generative-ai"
import { GOOGLE_API } from "@/config-global"
import { NextResponse } from "next/server"

// Configuration for the AI model
const AI_CONFIG = {
  temperature: 0.7,
  maxOutputTokens: 100,
}

const apiKey = GOOGLE_API.apiKey
const apiModel = GOOGLE_API.model

// Template for generating the prompt
const generateMessagePrompt = (message: string, tone: string) =>
  `
You are an expert content creator with over 10 years of experience crafting compelling and engaging content across various industries. Your task is to rewrite the following text in a ${tone} tone while maintaining its core message.

### Guidelines:
- Retain the original intent while enhancing impact.
- Apply engagement techniques based on your expertise.
- Ensure a natural flow that resonates with the target audience.
- Maintain a consistent ${tone} tone throughout.
- Deliver clean, formatted text without any special markup (no markdown, HTML, or unnecessary line breaks).

### Original Text:
"${message}"

### Rewritten Version:
`.trim()

// Initialize AI model
const initializeAIModel = (apiKey: string, modelName: string) => {
  const genAI = new GoogleGenerativeAI(apiKey)
  return genAI.getGenerativeModel({
    model: modelName,
    generationConfig: AI_CONFIG,
  })
}

// const generateImagePrompt = (rewrittenText: string) =>
//   `
// You are an expert prompt engineer specializing in Flux AI image generation. Your task is to create a highly detailed and visually rich prompt based on the following rewritten text.

// ### **Instructions:**
// - Convert the rewritten text into a **descriptive image prompt**.
// - Use **clear and vivid details** (e.g., colors, lighting, composition, perspective).
// - Specify **the environment, characters, objects, mood, and action**.
// - Ensure the prompt is **structured for optimal rendering** in Flux AI.

// ### **Rewritten Text:**
// "${rewrittenText}"

// ### **Flux AI Image Prompt:**
// `.trim()

// Handle AI text generation
const messageToAi = async (message: string, tone: string) => {
  if (!message || !tone) {
    throw new Error("Message and tone are required")
  }

  try {
    const model = initializeAIModel(apiKey, apiModel)
    const prompt = generateMessagePrompt(message, tone)
    const result = await model.generateContent(prompt)

    if (!result.response) {
      throw new Error("No response from AI model")
    }

    return result.response.text()
  } catch (error: any) {
    console.log("🚀 ~ messageToAi ~ error:", error)
    throw new Error(`AI generation failed: ${error.message}`)
  }
}

export async function POST(req: Request) {
  try {
    const { message, tone } = await req.json()
    const response = (await messageToAi(message, tone)) as any

    return NextResponse.json(response, { status: 200 })
  } catch (error: any) {
    console.error("🚀 ~ POST ~ error:", error)
    return NextResponse.json(
      { message: { error: error.message } },
      { status: 500 }
    )
  }
}
