// Reference: https://docs.plausible.io/api/analytics-api/

import { ANALYTIC_API } from "@/config-global"
import { NextResponse } from "next/server"

// // Type
// type T_Payload = {
//   site_id: string
//   date_range: string
//   metrics: string[]
//   // optional:
//   dimensions: any
//   filters: any
//   order_by: any
//   include: any
//   pagination: any
// }

// Helper

// GET /api/analytics
export async function GET(req: Request) {
  try {
    if (!ANALYTIC_API.url || !ANALYTIC_API.apiKey) {
      return NextResponse.json(
        { error: "Missing API key or site ID" },
        { status: 500 },
      )
    }
    // const { searchParams } = new URL(req.url)
    // const startDate = searchParams.get("start_date")
    // let endDate = searchParams.get("end_date")
    // const dateRange = searchParams.get("date_range") || "7d"
    // const metrics = searchParams.get("metrics")?.split(",") || ["visitors"]
    // const dimensions = searchParams.get("dimensions")?.split(",") || []
    // const filters = searchParams.get("filters")
    //   ? JSON.parse(searchParams.get("filters")!)
    //   : []

    // if (startDate && !endDate) {
    //   endDate = new Date().toISOString().split("T")[0]
    // }

    // let dateRangePayload
    // if (startDate && endDate) {
    //   dateRangePayload = [startDate, endDate]
    // } else if (dateRange) {
    //   dateRangePayload = dateRange
    // } else {
    //   dateRangePayload = "7d"
    // }

    // const payload = {
    //   site_id: process.env.SITE_ID,
    //   date_range: dateRangePayload,
    //   metrics,
    //   ...(dimensions.length && { dimensions }),
    //   ...(filters.length && { filters }),
    // }

    // const body = req.body
    // console.log("🚀 ~ GET ~ body:", body)

    const payload = {
      site_id: "crefion.com",
      metrics: [
        "visitors",
        "pageviews",
        "bounce_rate",
        "events",
        "visit_duration",
      ],
      date_range: "7d",
      filters: [["is", "event:hostname", ["vincent.crefion.com"]]],
      dimensions: ["time:hour"],
      include: {
        time_labels: true,
      },
    }

    const response = await fetch(`${ANALYTIC_API.url}/query`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${ANALYTIC_API.apiKey}`,
      },
      body: JSON.stringify(payload),
    }).catch((err) => {
      console.error("222:", err)
      return NextResponse.json(
        { error: "Internal Server Error" },
        { status: 500 },
      )
    })

    console.log("🚀 ~ GET ~ response:", response)
    if (!response.ok) {
      return NextResponse.json(
        { error: "Failed to fetch metrics" },
        { status: response.status },
      )
    }

    const data = await response.json()
    return NextResponse.json(data)
  } catch (error: any) {
    console.error("Error fetching Plausible metrics:", error)
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 },
    )
  }
}
