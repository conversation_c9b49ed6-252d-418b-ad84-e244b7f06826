import { redisConfig } from "@/config-global"
import { getSiteData } from "@/lib/appwrite-actions/admin/domain.action"
import { getSiteIdByDomain } from "@/lib/kv/get-site-kv"
import { NextRequest, NextResponse } from "next/server"

const getSiteId = async (domain: string) => {
  const siteData = await getSiteData(domain)
  return siteData?.site?.$id
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const domain = searchParams.get("domain")

  if (!domain) {
    return NextResponse.json({ error: "Domain is required" }, { status: 400 })
  }

  let siteId = null

  if (redisConfig.enabled) {
    siteId = await getSiteIdByDomain(domain)
    if (!siteId) {
      siteId = await getSiteId(domain)
    }
  } else {
    siteId = await getSiteId(domain)
  }

  if (!siteId) {
    return NextResponse.json({ error: "Site not found" }, { status: 404 })
  }

  return NextResponse.json({ siteId })
}
