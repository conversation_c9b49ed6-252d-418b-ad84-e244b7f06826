import { NextRequest, NextResponse } from 'next/server'

async function getGeoData() {
  try {
    const response = await fetch('https://ip-api.com/json/')
    const data = await response.json()
    return { 
      country: data.countryCode || 'US',
      city: data.city,
      region: data.regionName,
      timezone: data.timezone,
      ip: data.query
    }
  } catch (error) {
    console.error('Failed to fetch geo data:', error)
    return { 
      country: 'US',
      city: 'Unknown',
      region: 'Unknown',
      timezone: 'UTC',
      ip: 'Unknown'
    }
  }
}

function getLanguageFromCountry(countryCode: string): string {
  const countryToLanguage: Record<string, string> = {
    'VN': 'vi',
    'US': 'en',
    'GB': 'en',
    'AU': 'en',
    'CA': 'en',
    'JP': 'ja',
    'KR': 'ko',
    'TH': 'th',
    'CN': 'zh',
    'TW': 'zh',
    'HK': 'zh',
    'FR': 'fr',
    'DE': 'de',
    'ES': 'es',
    'IT': 'it',
    'RU': 'ru',
    'BR': 'pt',
    'PT': 'pt',
    'IN': 'hi',
    'ID': 'id',
    'MY': 'ms',
    'SG': 'en',
    'PH': 'en',
  }
  
  return countryToLanguage[countryCode] || 'en'
}

export async function GET(request: NextRequest) {
  const hostname = request.headers.get('host') || ''
  const isLocalhost = hostname.includes('localhost')
  
  let geoData
  let detectionMethod = ''
  
  if (isLocalhost) {
    // Localhost: call real IP API
    geoData = await getGeoData()
    detectionMethod = 'ip-api.com'
  } else {
    // Production: use Vercel headers
    const country = request.geo?.country || 
                   request.headers.get('x-vercel-ip-country') || 'US'
    const city = request.geo?.city || 
                request.headers.get('x-vercel-ip-city') || 'Unknown'
    const region = request.geo?.region || 
                  request.headers.get('x-vercel-ip-country-region') || 'Unknown'
    const timezone = request.headers.get('x-vercel-ip-timezone') || 'UTC'
    
    geoData = {
      country,
      city,
      region,
      timezone,
      ip: request.ip || 'Unknown'
    }
    detectionMethod = 'vercel-headers'
  }

  const detectedLanguage = getLanguageFromCountry(geoData.country)

  return NextResponse.json({
    ...geoData,
    detectedLanguage,
    detectionMethod,
    isLocalhost,
    hostname,
    userAgent: request.headers.get('user-agent'),
    timestamp: new Date().toISOString()
  })
}
