import { NextRequest, NextResponse } from "next/server"
import { VERCEL_CONFIG } from "@/config-global"

const headers = {
  Authorization: `Bearer ${VERCEL_CONFIG.token}`,
  "Content-Type": "application/json",
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { domains } = body

    if (!domains || !Array.isArray(domains)) {
      return NextResponse.json(
        { error: "Domains array is required" },
        { status: 400 },
      )
    }

    if (domains.length === 0) {
      return NextResponse.json(
        { error: "At least one domain is required" },
        { status: 400 },
      )
    }

    if (domains.length > 50) {
      return NextResponse.json(
        { error: "Maximum 50 domains allowed per request" },
        { status: 400 },
      )
    }

    if (!VERCEL_CONFIG.token) {
      return NextResponse.json(
        { error: "Vercel token is required" },
        { status: 500 },
      )
    }

    // Process all domains in parallel on server-side
    const results = await Promise.all(
      domains.map(async (domain: string) => {
        try {
          // Parallel calls for status and price
          const [statusResponse, priceResponse] = await Promise.allSettled([
            fetch(
              `${VERCEL_CONFIG.apiBase}/v4/domains/status?name=${encodeURIComponent(domain)}`,
              { headers },
            ),
            fetch(
              `${VERCEL_CONFIG.apiBase}/v4/domains/price?name=${encodeURIComponent(domain)}&type=new`,
              { headers },
            ),
          ])

          let available = false
          let price: number | undefined
          let period: number | undefined

          // Process status response
          if (
            statusResponse.status === "fulfilled" &&
            statusResponse.value.ok
          ) {
            const statusData = await statusResponse.value.json()
            available = statusData.available
          }

          // Process price response (only if available)
          if (
            available &&
            priceResponse.status === "fulfilled" &&
            priceResponse.value.ok
          ) {
            const priceData = await priceResponse.value.json()
            price = priceData.price
            period = priceData.period
          }

          return {
            name: domain,
            available,
            price,
            period,
            error: null,
          }
        } catch (error) {
          console.error(`Error checking domain ${domain}:`, error)
          return {
            name: domain,
            available: false,
            price: undefined,
            period: undefined,
            error: error instanceof Error ? error.message : "Unknown error",
          }
        }
      }),
    )

    // Add cache headers for better performance
    const response = NextResponse.json({
      domains: results,
      total: domains.length,
      timestamp: new Date().toISOString(),
    })

    response.headers.set(
      "Cache-Control",
      "public, s-maxage=180, stale-while-revalidate=300",
    )

    return response
  } catch (error) {
    console.error("Error in batch domain search API:", error)
    return NextResponse.json(
      { error: "Failed to process batch domain search" },
      { status: 500 },
    )
  }
}
