import { checkDomainAvailability } from "@/lib/vercel/vercel-api"
import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const domain = searchParams.get("domain")

    if (!domain) {
      return NextResponse.json(
        { error: "Domain parameter is required" },
        { status: 400 },
      )
    }

    const result = await checkDomainAvailability(domain)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in check-availability API:", error)
    return NextResponse.json(
      { error: "Failed to check domain availability" },
      { status: 500 },
    )
  }
}
