import { addDomainToProject, getProjectDomain } from "@/lib/vercel/vercel-api"
import { NextRequest, NextResponse } from "next/server"

/**
 * GET /api/domains/project
 *
 * @param request - The request object
 * @returns The response object
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const domain = searchParams.get("domain") || ""

    if (!domain) {
      return NextResponse.json({ error: "Domain is required" }, { status: 400 })
    }

    const projectDomain = await getProjectDomain(domain)

    return NextResponse.json(projectDomain)
  } catch (error) {
    console.error("Error in project domain API:", error)
    return NextResponse.json(
      { error: "Failed to get project domain" },
      { status: 500 },
    )
  }
}

/**
 * POST /api/domains/new
 *
 * @body {
 *  name: string
 *  redirect?: string
 *  redirectStatusCode?: number
 * }
 *
 * @returns json
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    const { name, redirect, redirectStatusCode } = body

    if (!name) {
      return NextResponse.json(
        { error: "Domain parameter is required" },
        { status: 400 },
      )
    }

    const result = await addDomainToProject(name, redirect, redirectStatusCode)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in new domain API:", error)
    return NextResponse.json({ error: "Failed to add domain" }, { status: 500 })
  }
}
