import { getDomainTransferInfo } from "@/lib/vercel/vercel-api"
import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const domain = searchParams.get("domain")

    if (!domain) {
      return NextResponse.json(
        { error: "Domain parameter is required" },
        { status: 400 },
      )
    }

    const result = await getDomainTransferInfo(domain)

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error in transfer-info API:", error)
    return NextResponse.json(
      { error: "Failed to get domain transfer info" },
      { status: 500 },
    )
  }
}
