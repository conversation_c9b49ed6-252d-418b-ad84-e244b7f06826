import iconData from "@/icon-data/icon-data.json"
import { NextRequest, NextResponse } from "next/server"

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const limit = Number.parseInt(searchParams.get("limit") || "100", 10)
  const skip = Number.parseInt(searchParams.get("skip") || "0", 10)
  const keyword = searchParams.get("keyword")?.toLocaleLowerCase()
  const icons = Object.keys(iconData)

  if (keyword && keyword.length > 0) {
    const filteredIcons = icons.filter((icon) => {
      const searchTerms = iconData[icon as keyof typeof iconData]
      return (
        Array.isArray(searchTerms) &&
        searchTerms.some((term) => term.toLocaleLowerCase().includes(keyword))
      )
    })
    return NextResponse.json(filteredIcons.slice(skip, skip + limit))
  }

  return NextResponse.json(icons.slice(skip, skip + limit))
}
