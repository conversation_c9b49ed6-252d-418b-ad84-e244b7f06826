/**
 * TikTok Page
 * @description This page is used to display the TikTok page
 * @params
 * - heading: string
 * - subheading: string
 * - items: {
 *    images: string[]
 *    direction: "horizontal" | "vertical"
 * }[]
 * - type: "grid" | "list" | "carousel" | "masonry"
 *
 * @returns
 * - GalleryPage component
 *
 * **/

import {
  _MOCK_CAROUSEL_IMG,
  _MOCK_CAROUSEL_IMG_LAYOUT_2,
} from "@/_mock/_carousel-img"
import {
  GalleryTemplate,
  GalleryTemplateLayout2,
} from "@/modules/gallery/templates"

export default function GalleryPage() {
  return (
    <div className="flex w-full flex-col gap-10 py-10">
      <GalleryTemplate {..._MOCK_CAROUSEL_IMG} />
      <GalleryTemplateLayout2 {..._MOCK_CAROUSEL_IMG_LAYOUT_2} />
    </div>
  )
}
