import AnimatedButton from "@/animations/components/animated-button"
import GsapScrambled from "@/animations/components/gsap-scrambled"
import Heading from "@/animations/components/heading"
import Section from "@/animations/components/section"

import { GsapTextAnimate } from "@/animations/components"

export default function GsapPage() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center gap-16 p-8">
      <Section className="h-screen">
        <Heading text="Welcome to GSAP + Next.js + TypeScript" />
        <AnimatedButton>Get Started</AnimatedButton>
      </Section>

      <Section className="h-screen">
        <Heading
          text="Another Animated Section"
          options={{
            y: 20,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
              trigger: ".heading",
              start: "top 80%",
              toggleActions: "play none none reverse",
            },
          }}
        />

        <GsapTextAnimate text="This one animates by word and long text lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos." />

        <AnimatedButton>Learn More</AnimatedButton>
      </Section>

      <GsapScrambled
        text="Scrambled Headline ✨ with options delay and scrollTrigger"
        options={{
          delay: 0.5,
          scrollTrigger: true,
        }}
      />
    </main>
  )
}
