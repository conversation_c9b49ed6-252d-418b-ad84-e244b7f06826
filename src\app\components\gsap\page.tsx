import { But<PERSON> } from "@/components/ui"
import Link from "next/link"

export default function GsapPage() {
  return (
    <main className="flex h-screen w-screen flex-col items-center justify-center gap-4 bg-black/10">
      <Link href="/components/gsap/demo">
        <Button>Demo</Button>
      </Link>
      <Link href="/components/gsap/observer">
        <Button>Observer</Button>
      </Link>
      <Link href="/components/gsap/scroll-smoother">
        <Button>GsapScrollSmoother</Button>
      </Link>
      <Link href="/components/gsap/gsap-text-animate">
        <Button>GsapTextAnimate</Button>
      </Link>
    </main>
  )
}
