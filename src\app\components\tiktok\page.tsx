/**
 * TikTok Page
 * @description This page is used to display the TikTok page
 * @params
 * - heading: string
 * - subheading: string
 * - limit: number
 * - type: "grid" | "list"
 * - items: {
 *    title: string
 *    url: string
 *    image: string
 *    description: string
 * }[]
 *
 * @returns
 * - TikTokPage component
 *
 * **/

import { _MOCK_TIKTOK_VIDEOS } from "@/_mock/_tiktok-videos"
import { TikTokVideosLayout1 } from "@/modules/tiktok-videos/templates"

export default function TikTokPage() {
  return (
    <div className="w-full max-w-screen-md">
      <TikTokVideosLayout1 {..._MOCK_TIKTOK_VIDEOS} />
    </div>
  )
}
