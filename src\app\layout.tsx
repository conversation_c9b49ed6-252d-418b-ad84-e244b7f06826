import { cn } from "@/utils"
import { Montser<PERSON> } from "next/font/google"

import { RootProvider } from "@/providers/root"
import "styles/globals.css"

const montserrat = Montserrat({
  subsets: ["latin"],
  variable: "--font-montserrat",
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html
      lang="en"
      data-mode="light"
      className={cn(montserrat.className, "antialiased")}
      suppressHydrationWarning={false}
    >
      <body>
        <RootProvider>
          <main className="relative">{children}</main>
        </RootProvider>
      </body>
    </html>
  )
}
