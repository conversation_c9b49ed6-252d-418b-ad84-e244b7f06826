import { getSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"
import { SiteBuilder } from "@/modules/site-builder"
import { paths } from "@/routes/paths"
import { I_WebsiteResponse } from "@/types/app/website"
import { Metadata } from "next"
import { notFound, redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "Website Builder | Crefion",
}

type TProps = {
  params: {
    id: string
  }
  searchParams: {
    pageId: string
  }
}

export default async function WebsiteBuilder({ params, searchParams }: TProps) {
  const { id } = await params
  const { pageId } = await searchParams

  const website = await getSiteDetail(id)

  !website && notFound()
  website.pages?.[0] &&
    !pageId &&
    redirect(paths.app.websites.builder(id, website.pages?.[0]?.$id))

  return (
    <SiteBuilder website={website as I_WebsiteResponse} isLiveView={false} />
  )
}
