import { getSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"
import { getBlogDetail } from "@/lib/appwrite-actions/store/blogs"
import { SiteLiveViewBlogDetails } from "@/modules/site-live-view"
import { T_BlogDocument } from "@/types/app/blog"
import { I_WebsiteResponse } from "@/types/app/website"
import { Metadata } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Blogs | Crefion",
}

type TProps = {
  params: {
    handle: string
    id: string
  }
}

export default async function WebsiteBlogsPage({ params }: TProps) {
  const { id, handle } = await params
  const website = await getSiteDetail(id)

  !website && notFound()

  const details = await getBlogDetail(handle)

  return (
    <Suspense
      fallback={
        <div className="flex h-screen w-full items-center justify-center">
          Loading...
        </div>
      }
    >
      <SiteLiveViewBlogDetails
        website={website as I_WebsiteResponse}
        handle={handle}
        details={details as T_BlogDocument}
      />
    </Suspense>
  )
}
