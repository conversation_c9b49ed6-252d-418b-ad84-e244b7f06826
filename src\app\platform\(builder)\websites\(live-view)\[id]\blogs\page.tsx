import { getSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"
import SiteLiveViewBlogList from "@/modules/site-live-view/template-blog-lists"
import { I_WebsiteResponse } from "@/types/app/website"
import { Metadata } from "next"
import { notFound } from "next/navigation"

export const metadata: Metadata = {
  title: "Blogs | Crefion",
}

type TProps = {
  params: {
    path: string
    id: string
  }
}

export default async function WebsiteBlogsPage({ params }: TProps) {
  const { id, path } = await params
  const website = await getSiteDetail(id)

  !website && notFound()

  return (
    <SiteLiveViewBlogList
      website={website as I_WebsiteResponse}
      pagePath={path}
    />
  )
}
