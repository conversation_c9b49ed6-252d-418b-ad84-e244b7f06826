import { Skeleton } from "@/components/ui/skeleton"
import { getPublishedSiteDetail } from "@/lib/appwrite-actions/admin/site-builder.action"
import { SiteLiveView } from "@/modules/site-live-view"
import { I_WebsiteResponse } from "@/types/app/website"
import { Metadata } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Website Builder | Crefion",
  description: "Website Builder | Crefion",
}

type TProps = {
  params: {
    id: string
  }
  searchParams: {
    pageId: string
  }
}

export default async function WebsiteLiveView({
  params,
  searchParams,
}: TProps) {
  const { id } = await params
  // const website = await getSiteDetail(id)

  const handle = "home"
  const website = await getPublishedSiteDetail(id, handle)

  !website && notFound()

  return (
    <Suspense fallback={<Skeleton className="h-full w-full" />}>
      <SiteLiveView
        website={website as I_WebsiteResponse}
        // storeProviders={storeProviders as any}
        // cartDataMedusa={cartItems}
      />
    </Suspense>
  )
}
