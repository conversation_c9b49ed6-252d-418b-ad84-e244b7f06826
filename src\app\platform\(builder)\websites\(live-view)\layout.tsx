"use client"

import { GsapScrollSmoother } from "@/animations/components"
import { plausible } from "@/config-global"
import FooterCopyRight from "@/modules/site-builder/components/preview/footer/copyright"
import PlausibleProvider from "next-plausible"

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <PlausibleProvider
        domain={plausible.domain}
        customDomain={plausible.customHost}
        selfHosted
        trackOutboundLinks
        trackFileDownloads
        enabled={plausible.domain.indexOf("localhost") !== -1 || undefined}
        trackLocalhost={plausible.domain.indexOf("localhost") !== -1}
      />

      <GsapScrollSmoother>{children}</GsapScrollSmoother>
      <FooterCopyRight />
    </>
  )
}
