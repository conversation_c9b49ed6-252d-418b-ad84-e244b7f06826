import { AppHeader } from "@/components/layout/app-header"
import LoadingContent from "@/components/loading/loading-content"
import { getFormList } from "@/lib/appwrite-actions/admin/forms.action"
import FormListTemplate from "@/modules/forms-submissions/templates/form-list-template"
import { Metadata } from "next"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Forms | Crefion",
}

export default async function FormListPage() {
  const forms = await getFormList()
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Forms",
            href: "#!",
          },
        ]}
      />
      <Suspense fallback={<LoadingContent />}>
        <FormListTemplate formResponse={forms} />
      </Suspense>
    </div>
  )
}
