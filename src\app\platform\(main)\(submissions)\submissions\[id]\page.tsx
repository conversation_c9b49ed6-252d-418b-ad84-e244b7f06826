import Loading from "@/app/loading"
import { getSubmissionList } from "@/lib/appwrite-actions/admin/submissions.action"
import SubmissionDetailTemplate from "@/modules/forms-submissions/templates/submission-detail-template"
import { T_SubmissionResponse } from "@/types/app/submission"
import { notFound } from "next/navigation"
import { Suspense } from "react"

type ListOfBlogProps = {
  params: {
    id: string
  }
  searchParams: {
    status: string
    page: string
    limit: string
  }
}
// Main Server Component
export default async function SubmissionDetailPage({
  params,
  searchParams,
}: ListOfBlogProps) {
  const { id } = await params
  const { status = "ALL", page = "1", limit = "10" } = await searchParams

  const submissions = await getSubmissionList({
    form_id: id,
    status: status === "ALL" ? undefined : (status as "ACTIVE" | "NOT_ACTIVE"),
    limit: parseInt(limit),
    offset: (parseInt(page) - 1) * parseInt(limit),
  })

  if (!submissions) {
    return notFound()
  }

  return (
    <Suspense fallback={<Loading />}>
      <SubmissionDetailTemplate
        submissions={submissions as T_SubmissionResponse}
      />
    </Suspense>
  )
}
