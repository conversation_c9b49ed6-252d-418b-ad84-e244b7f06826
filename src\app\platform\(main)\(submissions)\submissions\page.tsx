import AppHeader from "@/components/layout/app-header"
import { notFound } from "next/navigation"

import { T_SubmissionResponse } from "@/types/app/submission"

import Loading from "@/app/loading"
import { getFormList } from "@/lib/appwrite-actions/admin/forms.action"
import { getSubmissionList } from "@/lib/appwrite-actions/admin/submissions.action"
import SubmissionListTemplate from "@/modules/forms-submissions/templates/submission-list-template"
import { Suspense } from "react"

type ListOfBlogProps = {
  searchParams: {
    status: string
    search: string
    page: string
    limit: string
  }
}
// Main Server Component
export default async function SubmissionListPage({
  searchParams,
}: ListOfBlogProps) {
  const {
    status = "ALL",
    search = "",
    page = "1",
    limit = "10",
  } = await searchParams

  const submissions = await getSubmissionList({})

  const forms = await getFormList({
    search: search === "" ? undefined : search,
    status: status === "ALL" ? undefined : (status as "ACTIVE" | "NOT_ACTIVE"),
    limit: parseInt(limit),
    offset: (parseInt(page) - 1) * parseInt(limit),
  })

  if (!submissions) {
    return notFound()
  }

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Submissions",
            href: "#!",
          },
        ]}
      />
      <Suspense fallback={<Loading />}>
        <SubmissionListTemplate
          submissions={submissions as T_SubmissionResponse}
          forms={forms}
        />
      </Suspense>
    </div>
  )
}
