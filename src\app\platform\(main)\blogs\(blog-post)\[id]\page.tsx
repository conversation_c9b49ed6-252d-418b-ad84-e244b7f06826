import {
  getDocumentDetail,
  getDocuments,
} from "@/appwrite/actions/document.actions"
import { APP_WRITE } from "@/config-global"
import FormBlogTemplate from "@/modules/blogs/templates/form-blog-template"
import { T_BlogDocument } from "@/types/app/blog"
import { T_BlogCategoryResponse } from "@/types/app/category-blog"

const getCategoriesBlogs = async () => {
  const blogPost = await getDocuments({
    collectionId: APP_WRITE.collections.blogCategory,
  })
  return blogPost as T_BlogCategoryResponse
}

async function getBlog(idBlog: string): Promise<T_BlogDocument> {
  const blogs = await getDocumentDetail({
    collectionId: APP_WRITE.collections.blogs,
    documentId: idBlog,
  })
  return blogs as T_BlogDocument
}

type EditBlogPageProps = {
  params: { id: string }
}
// Main Server Component
export default async function EditBlogPage({ params }: EditBlogPageProps) {
  const { id } = await params
  const blog = await getBlog(id)
  const categoriesBlogs = await getCategoriesBlogs()
  return <FormBlogTemplate dataEdit={blog} categoriesBlogs={categoriesBlogs} />
}
