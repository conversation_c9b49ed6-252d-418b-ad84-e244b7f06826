import { getDocuments } from "@/appwrite/actions/document.actions"
import AppHeader from "@/components/layout/app-header"
import { APP_WRITE } from "@/config-global"
import FormBlogTemplate from "@/modules/blogs/templates/form-blog-template"
import { T_BlogCategoryResponse } from "@/types/app/category-blog"

const getCategoriesBlogs = async () => {
  const blogPost = await getDocuments({
    collectionId: APP_WRITE.collections.blogCategory,
  })
  return blogPost as T_BlogCategoryResponse
}
// Main Server Component
export default async function CreateBlogPage() {
  const categoriesBlogs = await getCategoriesBlogs()
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Submissions",
            href: "#!",
          },
        ]}
      />
      <FormBlogTemplate categoriesBlogs={categoriesBlogs} />
    </div>
  )
}
