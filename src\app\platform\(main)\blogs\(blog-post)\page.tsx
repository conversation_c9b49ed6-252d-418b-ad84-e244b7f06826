import AppHeader from "@/components/layout/app-header"
import { getBlogList } from "@/lib/appwrite-actions/admin/blogs"
import ListOfBlogsTemplate from "@/modules/blogs/templates/list-blogs-template"
import { T_BlogResponse } from "@/types/app/blog"

type ListOfBlogProps = {
  searchParams: {
    status: string
    search: string
  }
}
// Main Server Component
export default async function ListOfBlogPage({
  searchParams,
}: ListOfBlogProps) {
  const { status, search } = await searchParams
  // const blogs = await getBlogs(status, search)

  const blogs = await getBlogList({
    status: status === "all" ? undefined : (status as "ACTIVE" | "NOT_ACTIVE"),
    search: search === "" ? undefined : search,
  })

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Blogs",
            href: "#!",
          },
        ]}
      />
      <ListOfBlogsTemplate blogs={blogs as T_BlogResponse} />
    </div>
  )
}
