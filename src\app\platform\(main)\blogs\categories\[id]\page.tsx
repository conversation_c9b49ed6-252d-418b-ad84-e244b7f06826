import { getDocumentDetail } from "@/appwrite/actions/document.actions"
import AppHeader from "@/components/layout/app-header"
import { APP_WRITE } from "@/config-global"
import FormCategoryBlogTemplate from "@/modules/blogs/templates/form-category-blog-template"
import { T_BlogCategoryDocument } from "@/types/app/category-blog"

async function getCategoryBlog(
  idCategoryBlog: string,
): Promise<T_BlogCategoryDocument> {
  const categoryBlogs = await getDocumentDetail({
    collectionId: APP_WRITE.collections.blogCategory,
    documentId: idCategoryBlog,
  })
  return categoryBlogs as T_BlogCategoryDocument
}

type EditCategoryBlogPageProps = {
  params: { id: string }
}
// Main Server Component
export default async function EditCategoryBlogPage({
  params,
}: EditCategoryBlogPageProps) {
  const { id } = await params
  const categoryBlog = await getCategoryBlog(id)
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Blogs",
            href: "#!",
          },
        ]}
      />
      <FormCategoryBlogTemplate dataEdit={categoryBlog} />
    </div>
  )
}
