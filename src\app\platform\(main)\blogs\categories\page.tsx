import AppHeader from "@/components/layout/app-header"
import { getBlogCategories } from "@/lib/appwrite-actions/admin/blog-categories"
import CategoriesBlogsTemplate from "@/modules/blogs/templates/list-categories-blogs-template"
import { T_BlogCategoryResponse } from "@/types/app/category-blog"

type ListOfBlogProps = {
  searchParams: {
    status: string
    search: string
  }
}
// Main Server Component
export default async function ListOfBlogPage({
  searchParams,
}: ListOfBlogProps) {
  const { status, search } = await searchParams

  const categoriesBlogs = await getBlogCategories({
    status: status === "all" ? undefined : (status as "ACTIVE" | "NOT_ACTIVE"),
    search: search === "" ? undefined : search,
  })

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Blogs",
            href: "#!",
          },
        ]}
      />
      <CategoriesBlogsTemplate
        categoriesBlogs={categoriesBlogs as T_BlogCategoryResponse}
      />
    </div>
  )
}
