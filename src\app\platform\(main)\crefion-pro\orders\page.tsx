import { AppHeader } from "@/components/layout/app-header"
import { ProOrderTransactions } from "@/modules/crefion-pro/templates"
import { paths } from "@/routes/paths"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Transactions | Crefion",
}

export default function PlatformOrders() {
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Crefion Pro",
            href: paths.app.crefionPro.root,
          },
          {
            title: "Transactions",
            href: "#!",
          },
        ]}
      />

      <ProOrderTransactions />
    </div>
  )
}
