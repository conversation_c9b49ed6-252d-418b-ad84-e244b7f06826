import { Metadata } from "next"

import AppHeader from "@/components/layout/app-header"
import { ProPayment } from "@/modules/crefion-pro/templates"
import { paths } from "@/routes/paths"

export const metadata: Metadata = {
  title: "Crefion Pro",
  description: "Crefion Pro",
}

export default async function CrefionProPage() {
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Crefion Pro",
            href: paths.app.crefionPro.root,
          },
          {
            title: "Payment",
            href: "#!",
          },
        ]}
      />
      <ProPayment hideHeader />
    </div>
  )
}
