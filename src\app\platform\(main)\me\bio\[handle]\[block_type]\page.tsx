import Loading from "@/app/loading"
import AppHeader from "@/components/layout/app-header"
import { getBioBlockDataById } from "@/lib/appwrite-actions/admin/bio-block-data"
import {
  getBioPageByHandle,
  getBioPages,
} from "@/lib/appwrite-actions/admin/bio-page.action"
import DesignBioBlockForm from "@/modules/design/templates/design-bio-block-form"
import { Metadata } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Add Block | Crefion",
}

// ------------------------------------------------------------
// Page
// ------------------------------------------------------------

type Props = {
  params: {
    handle: string
    block_type: string
  }
  searchParams: {
    b: string
  }
}

export default async function PageBlockType({ params, searchParams }: Props) {
  const { handle } = await params
  const { b } = await searchParams

  const bioPage = await getBioPageByHandle(handle)
  const bioPages = await getBioPages()

  if (!b) return notFound()

  const bioBlock = await getBioBlockDataById(b)

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Link in Bio",
            href: "#",
          },
        ]}
      />
      <Suspense fallback={<Loading />}>
        <DesignBioBlockForm
          bioPages={bioPages}
          currentPage={bioPage}
          currentBlock={bioBlock}
        />
      </Suspense>
    </div>
  )
}
