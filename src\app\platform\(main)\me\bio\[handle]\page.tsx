// import {
//   getBioPageByHandle,
//   getBioPages,
// } from "@/lib/appwrite-actions/admin/bio-page.action"
import AppHeader from "@/components/layout/app-header"
import ListLinkInBio from "@/modules/design/templates/bio-link-list"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Link in Bio | Crefion",
}

// ------------------------------------------------------------
// Page
// ------------------------------------------------------------

// type Props = {
//   params: {
//     handle: string
//   }
// }

export default async function PageBioDetails() {
  // const { handle } = await params
  // const bioPage = await getBioPageByHandle(handle)
  // const bioPages = await getBioPages()

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Link in Bio",
            href: "#",
          },
        ]}
      />
      <ListLinkInBio />
    </div>
  )
  // return <DesignLinkInBio bioPages={bioPages} currentBioPage={bioPage} />
}
