import AppHeader from "@/components/layout/app-header"
import { getOrders } from "@/lib/appwrite-actions/admin/order"
import { getStoreProviders } from "@/lib/appwrite-actions/admin/store-provider"
import AppHome from "@/modules/app-home/templates"
import { T_OrderResponseMedusa } from "@/types/app/orders"
import { getBaseURL } from "@/utils"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Home | Crefion",
  description: "Crefion is a simple, fast, and reliable URL for your store.",
  icons: {
    icon: "/favicon.ico",
  },
  metadataBase: new URL(getBaseURL()),
  openGraph: {
    images: [
      {
        url: "/thumbnail.png",
        alt: "Crefion",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    images: ["/thumbnail.png"],
  },
}

type TProps = {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

const fetchDataOrders = async () => {
  const provider = await getStoreProviders()
  if (!provider || provider.length === 0) {
    return { totalOrders: 0, totalAmount: 0 }
  }
  const orders = (await getOrders({
    secretKey: provider[0].api_secret,
    salesChannelId: provider[0].tracking_id,
  })) as T_OrderResponseMedusa

  if (!orders || orders?.count === 0) {
    return { totalOrders: 0, totalAmount: 0 }
  }

  const totalOrders = orders.count
  const totalAmount = orders.orders.reduce(
    (acc, order) => acc + order.summary.accounting_total,
    0,
  )
  return { totalOrders, totalAmount }
}

export default async function App({ searchParams }: TProps) {
  const orders = await fetchDataOrders()

  const search = await searchParams
  return (
    <div className="w-full">
      <AppHeader />
      <AppHome searchParams={search} orders={orders} />
    </div>
  )
}
