import { Skeleton } from "@/components/ui"
import { listUserDomains } from "@/lib/appwrite-actions/admin/domain.action"
import { DomainList } from "@/modules/domain"
import { Metadata } from "next"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Domains | Crefion",
  description: "Manage your custom domains",
}

export default async function Domain() {
  const userDomains = await listUserDomains()

  return (
    <Suspense fallback={<Skeleton className="h-[500px] w-full" />}>
      <DomainList domains={userDomains} />
    </Suspense>
  )
}

export const dynamic = "force-dynamic"
