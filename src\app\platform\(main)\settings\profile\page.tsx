import AppHeader from "@/components/layout/app-header"
import ProfileView from "@/modules/account/templates/profile-view"
import { paths } from "@/routes/paths"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Profile | Crefion",
}

export default function Profile() {
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Profile",
            href: paths.app.settings.root,
          },
        ]}
      />
      <ProfileView />
    </div>
  )
}

// https://account.crefion.com/account-settings/home/<USER>
