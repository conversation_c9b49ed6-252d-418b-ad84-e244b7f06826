import { AppHeader } from "@/components/layout/app-header"
import SocialsView from "@/modules/socials/templates/socials-view"
import { paths } from "@/routes/paths"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Socials | Crefion",
}

export default function Socials() {
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Socials",
            href: paths.app.settings.root,
          },
        ]}
      />
      <SocialsView />
    </div>
  )
}
