import { AppHeader } from "@/components/layout/app-header"
import TrackingPixelsView from "@/modules/tracking-pixels/templates/tracking-pixels-view"
import { paths } from "@/routes/paths"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Tracking Pixels | Crefion",
}

//---------------------------------------------------------------------------------------

export default function TrackingPixelsPage() {
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Tracking Pixels",
            href: paths.app.settings.root,
          },
        ]}
      />
      <TrackingPixelsView />
    </div>
  )
}
