import { AppHeader } from "@/components/layout/app-header"
import { LoadingContent } from "@/components/loading"
import { getOrders } from "@/lib/appwrite-actions/admin/order"
import { getStoreProviders } from "@/lib/appwrite-actions/admin/store-provider"
import { AppStoreOrder } from "@/modules/app-store/templates"
import { paths } from "@/routes/paths"
import { T_OrderResponseMedusa } from "@/types/app/orders"
import { Metadata } from "next"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Orders | Crefion",
}

export default async function Orders() {
  const provider = await getStoreProviders()

  const orders = (await getOrders({
    secretKey: provider?.[0]?.api_secret,
    salesChannelId: provider?.[0]?.tracking_id,
  })) as T_OrderResponseMedusa

  if (!provider?.[0]?.api_secret || !provider?.[0]?.tracking_id) {
    return <div>No provider found</div>
  }

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Orders",
            href: paths.app.store.orders,
          },
        ]}
      />
      <Suspense fallback={<LoadingContent />}>
        <AppStoreOrder data={orders} />
      </Suspense>
    </div>
  )
}
