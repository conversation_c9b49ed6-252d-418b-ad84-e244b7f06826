import AppHeader from "@/components/layout/app-header"
import { Skeleton } from "@/components/ui"
import { getProductCategories } from "@/lib/appwrite-actions/admin/store-product-category"
import { getStoreProviders } from "@/lib/appwrite-actions/admin/store-provider"
import { AppStoreProductCategory } from "@/modules/app-store/templates"
import { Metadata } from "next"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Products | Crefion",
}

type TProps = {
  searchParams: {
    limit: string
    page: string
  }
}

export default async function Products({ searchParams }: TProps) {
  const storeProviders = await getStoreProviders()

  const { limit = "12", page = "1" } = await searchParams

  const pLimit = parseInt(limit) > 0 ? parseInt(limit) : 12
  const pPage = parseInt(page) > 0 ? parseInt(page) : 1

  const pQueries = {
    ...(pLimit && { limit: pLimit }),
    ...(pPage && { offset: (pPage - 1) * pLimit }),
  }
  const categoryData = await getProductCategories(pQueries)

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Product Categories",
            href: "#!",
          },
        ]}
      />
      <Suspense fallback={<Skeleton className="h-full w-full" />}>
        <AppStoreProductCategory
          storeProviders={storeProviders as any}
          categoryData={categoryData}
          limit={pLimit}
        />
      </Suspense>
    </div>
  )
}

export const dynamic = "force-dynamic"
