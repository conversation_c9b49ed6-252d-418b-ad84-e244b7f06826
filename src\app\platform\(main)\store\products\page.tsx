import AppHeader from "@/components/layout/app-header"
import { getProducts } from "@/lib/appwrite-actions/admin/store-product"
import { getStoreProviders } from "@/lib/appwrite-actions/admin/store-provider"
import { AppStoreProduct } from "@/modules/app-store/templates"
import SkeletonProductGrid from "@/modules/skeletons/templates/skeleton-product-grid"
import { Metadata } from "next"
import { Suspense } from "react"

export const metadata: Metadata = {
  title: "Products | Crefion",
}

type TProps = {
  searchParams: {
    limit: string
    page: string
  }
}

export default async function ProductCategories({ searchParams }: TProps) {
  const storeProviders = await getStoreProviders()

  const { limit = "12", page = "1" } = await searchParams

  const pLimit = parseInt(limit) > 0 ? parseInt(limit) : 12
  const pPage = parseInt(page) > 0 ? parseInt(page) : 1

  const pQueries = {
    ...(pLimit && { limit: pLimit }),
    ...(pPage && { offset: (pPage - 1) * pLimit }),
  }
  const productData = await getProducts(pQueries)

  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Store",
            href: "#!",
          },
        ]}
      />
      <Suspense fallback={<SkeletonProductGrid count={parseInt(limit)} />}>
        <AppStoreProduct
          storeProviders={storeProviders as any}
          productData={productData}
          limit={pLimit}
        />
      </Suspense>
    </div>
  )
}

export const dynamic = "force-dynamic"
