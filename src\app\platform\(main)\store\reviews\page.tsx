import { AppHeader } from "@/components/layout/app-header"
import { AppStoreReviews } from "@/modules/app-store/templates"
import { paths } from "@/routes/paths"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Reviews & Inquiries | Crefion",
}

export default function Reviews() {
  return (
    <div className="w-full">
      <AppHeader
        breadcrumbs={[
          {
            title: "Reviews & Inquiries",
            href: paths.app.store.reviews,
          },
        ]}
      />
      <AppStoreReviews />
    </div>
  )
}
