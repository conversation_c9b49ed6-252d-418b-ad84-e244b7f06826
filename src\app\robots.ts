import type { MetadataRoute } from "next"

import { appApiUrl } from "@/config-global"

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: "*",
        disallow: [
          "/platform",
          "/api",
          "/auth",
          "/components",
          "/_next",
          "/builder",
        ],
      },
    ],
    sitemap: `${appApiUrl}/sitemap.xml`,
  }
}
