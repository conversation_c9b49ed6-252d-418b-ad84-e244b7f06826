"use server"

import { Permission, Query, Role } from "node-appwrite"
import { createAdminClient, ID } from "../appwrite"
import { parseStringify } from "../utils"

import { APP_WRITE } from "@/config-global"
import { CrudFilter, Pagination } from "../types/query"
import { fDocument, fDocuments } from "./util"

const dbId = APP_WRITE.databaseId

export const getUserInfo = async ({ user_id }: any) => {
  try {
    const { database } = await createAdminClient()

    const user = await database.listDocuments(
      dbId!,
      APP_WRITE.collections.users!,
      [Query.equal("user_id", [user_id])],
    )

    return parseStringify(user.documents[0])
  } catch (error) {
    console.log(error)
    throw error
  }
}

export const getCollections = async () => {
  try {
    const { database } = await createAdminClient()

    const collections = await database.listCollections(dbId)

    return collections
  } catch (error) {
    console.log(error)
    throw error
  }
}

export const getCollection = async ({ collectionId }: any) => {
  try {
    const { database } = await createAdminClient()

    const collection = await database.getCollection(dbId, collectionId)

    return collection
  } catch (error) {
    console.log(error)
    throw error
  }
}

export const updateCollection = async ({ collectionId, data }: any) => {
  try {
    const { database } = await createAdminClient()

    const collection = await database.updateCollection(dbId, collectionId, data)

    return collection
  } catch (error) {
    console.log(error)
    throw error
  }
}

export const deleteCollection = async ({ collectionId }: any) => {
  try {
    const { database } = await createAdminClient()

    const collection = await database.deleteCollection(dbId, collectionId)

    return collection
  } catch (error) {
    console.log(error)
    throw error
  }
}

export const getDocument = async ({ collectionId, documentId }: any) => {
  try {
    const { database } = await createAdminClient()

    const document = await database.getDocument(dbId, collectionId, documentId)

    return fDocument(document)
  } catch (error) {
    console.log(error)
    throw error
  }
}

export type TDocumentList = {
  collectionId: string
  filters?: CrudFilter[]
  paging?: Pagination
  queries?: any
}

export const getDocuments = async ({
  collectionId,
  queries = [],
}: TDocumentList) => {
  try {
    // const uuid = ID.unique()

    // console.time(`fetch-documents ${collectionId} at ${uuid}`)

    const { database } = await createAdminClient()

    const documents = await database.listDocuments(dbId, collectionId, queries)

    // console.timeEnd(`fetch-documents ${collectionId} at ${uuid}`)

    const result = fDocuments(documents)
    return {
      ...result,
      total: documents.total,
    }
  } catch (error) {
    console.log(error)
    throw error
  }
}

type TDocumentDetail = {
  collectionId: string
  documentId: string
  filters?: CrudFilter[]
  paging?: Pagination
  queries?: any
}

export const getDocumentDetail = async ({
  collectionId,
  documentId,
  queries = [],
}: TDocumentDetail) => {
  try {
    // console.time("Fetch document detail")
    const { database } = await createAdminClient()

    // const queries = filters.map((filter) => {
    //   return Query[filter.operator](filter.field, filter.value);
    // })

    // const queries = filters.map((filter) => {
    //   return Query[filter.operator ](filter.field, filter.value);
    // })

    const documentDetail = await database.getDocument(
      dbId,
      collectionId,
      documentId,
      queries,
    )
    // console.timeEnd("Fetch document detail")

    return fDocument(documentDetail)
  } catch (error) {
    console.log(error)
    throw error
  }
}

export const createDocument = async ({
  collectionId,
  data,
  permissions,
}: any) => {
  try {
    // console.time("Create document")
    const { database } = await createAdminClient()
    const documentId = ID.unique()

    const document = await database.createDocument(
      dbId,
      collectionId,
      documentId,
      data,
      permissions || [
        Permission.read(Role.any()),
        Permission.update(Role.users()),
        Permission.delete(Role.users()),
      ],
    )
    // console.timeEnd("Create document")

    return fDocument(document)
  } catch (error) {
    console.log(error)
    throw error
  }
}

export async function updateDocument({ collectionId, documentId, data }: any) {
  try {
    // console.time("Update document")
    const { database } = await createAdminClient()

    const document = await database.updateDocument(
      dbId,
      collectionId,
      documentId,
      data,
    )
    // console.timeEnd("Update document")
    return fDocument(document)
  } catch (error) {
    console.error("🚀 ~ updateDocument ~ error:", error, collectionId)
    throw error
  }
}

export async function deleteDocument({ collectionId, documentId }: any) {
  try {
    // console.time("Delete document")
    const { database } = await createAdminClient()

    await database.deleteDocument(dbId, collectionId, documentId)
    // console.timeEnd("Delete document")
    return true
  } catch (error) {
    console.log(error)
    throw error
  }
}
