"use server"

import { ExecutionMethod } from "node-appwrite"
import { createAdminClient } from "../appwrite"
import { parseStringify } from "../utils"

export const executeFunction = async (name: string, data: any) => {
  try {
    const { functions } = await createAdminClient()
    const response = await functions.createExecution(
      name,
      JSON.stringify(data),
      true,
      undefined,
      ExecutionMethod.POST,
      {
        "Content-Type": "application/json; charset=utf-8",
      },
    )

    return parseStringify(response)
  } catch (error) {
    console.error(error)
    throw error
  }
}

export const getExecutions = async (functionId: string) => {
  try {
    const { functions } = await createAdminClient()
    const response = await functions.listExecutions(functionId)
    return parseStringify(response)
  } catch (error) {
    console.error(error)
    throw error
  }
}

export const getExecution = async (functionId: string, executionId: string) => {
  try {
    const { functions } = await createAdminClient()
    const response = await functions.getExecution(functionId, executionId)
    return parseStringify(response)
  } catch (error) {
    console.error(error)
    throw error
  }
}

export const deleteExecution = async (
  functionId: string,
  executionId: string,
) => {
  try {
    const { functions } = await createAdminClient()
    const response = await functions.deleteExecution(functionId, executionId)
    return parseStringify(response)
  } catch (error) {
    console.error(error)
    throw error
  }
}
