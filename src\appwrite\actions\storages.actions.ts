"use server"

import { APP_WRITE } from "@/config-global"
import { ID, Permission, Role } from "node-appwrite"

import { createAdminClient } from "../appwrite"
import { getFileUrl } from "../storage"
import { getLoggedInUser } from "./user.actions"

const defaultBucket = APP_WRITE.bucketId

const getCurrentUserId = async () => {
  const user = await getLoggedInUser()

  if (!user) {
    throw new Error("Authentication required")
  }

  return user.$id
}

// Upload single file
export const uploadFile = async ({ file }: { file: File }) => {
  try {
    if (!file) return
    const { storage } = await createAdminClient()

    const userId = await getCurrentUserId()

    const response = await storage.createFile(
      userId || defaultBucket,
      ID.unique(),
      file,
      [
        Permission.read(Role.any()),
        Permission.update(Role.user(userId)),
        Permission.delete(Role.user(userId)),
      ],
    )

    const url = getFileUrl(response.$id)

    return { ...response, url }
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Upload multiple files
export const uploadFiles = async ({ files }: { files: File[] }) => {
  try {
    const { storage } = await createAdminClient()

    const userId = await getCurrentUserId()
    const results: any[] = []

    const promises = files.map(async (file) => {
      const result = await storage.createFile(userId, ID.unique(), file, [
        Permission.read(Role.any()),
        Permission.update(Role.user(userId)),
        Permission.delete(Role.user(userId)),
      ])

      const url = getFileUrl(result.$id)

      results.push({ ...result, url })
    })

    await Promise.all(promises)

    return results
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Get all files
export const getFiles = async () => {
  try {
    const { storage } = await createAdminClient()

    const response = await storage.listFiles(defaultBucket)

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Get file by id
export const getFile = async ({ fileId }: { fileId: string }) => {
  try {
    const { storage } = await createAdminClient()

    const bucketId = await getCurrentUserId()

    const response = await storage.getFile(bucketId, fileId)

    const url = getFileUrl(response.$id)

    return { ...response, url }
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Get file view
export const getFileView = async ({ fileId }: { fileId: string }) => {
  try {
    const { storage } = await createAdminClient()

    const bucketId = await getCurrentUserId()

    const response = await storage.getFileView(bucketId, fileId)

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Get file preview
export const getFilePreview = async ({ fileId }: { fileId: string }) => {
  try {
    const { storage } = await createAdminClient()

    const bucketId = await getCurrentUserId()

    const response = await storage.getFilePreview(bucketId, fileId)

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Delete file
export const deleteFile = async ({ fileId }: { fileId: string }) => {
  try {
    const { storage } = await createAdminClient()

    const userId = await getCurrentUserId()

    const bucketId = userId || defaultBucket

    const response = await storage.deleteFile(bucketId, fileId)

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}

// Delete multiple files
export const deleteFiles = async ({ fileIds }: { fileIds: string[] }) => {
  try {
    const { storage } = await createAdminClient()

    const userId = await getCurrentUserId()
    const bucketId = userId || defaultBucket

    const response = await Promise.all(
      fileIds.map(async (fileId) => {
        return await storage.deleteFile(bucketId, fileId)
      }),
    )

    return response
  } catch (error) {
    console.error(error)
    throw error
  }
}
