"use server"

import { cookies } from "next/headers"
import { ID, Query } from "node-appwrite"
import { createAdminClient, createSessionClient } from "../appwrite"
import { parseStringify } from "../utils"

import { APP_WRITE, cookieName } from "@/config-global"
import { paths } from "@/routes/paths"
import { revalidateTag, unstable_cache } from "next/cache"
import { redirect } from "next/navigation"
import { getDocuments } from "./document.actions"

const WORKSPACE_COOKIE_NAME = `workspace_${cookieName}`
const MY_WORKSPACES_COOKIE_NAME = `workspaces_${cookieName}`

const userCollection = APP_WRITE.collections.users

// ------------------------------------------------------------------------------------
// export const setCookiesWorkspaces = async (user: Models.Document) => {
//   const slugs = user.workspaces.map((w: any) => w.slug).join(",")

//   const cookiesStore = await cookies()

//   cookiesStore.set(WORKSPACE_COOKIE_NAME, user.workspace, {})
//   cookiesStore.set(MY_WORKSPACES_COOKIE_NAME, slugs, {})
// }

// USER COLLECTION
export const getUserInfo = async ({ user_id }: any) => {
  try {
    const { database } = await createAdminClient()

    const cachedUser = unstable_cache(
      async () => {
        const user = await database.getDocument(
          APP_WRITE.databaseId!,
          userCollection!,
          user_id,
          // [
          //   Query.select([
          //     "$id",
          //     "username",
          //     "email",
          //     "avatar",
          //     "phone_number",
          //     "type",
          //     "plan",
          //   ]),
          // ],
        )

        return user
      },
      ["user", `user-info-${user_id}`],
      {
        tags: [`user-info-${user_id}`],
        revalidate: 60 * 60 * 24, // 24 hours
      },
    )

    // setCookiesWorkspaces(user)
    const user = await cachedUser()
    return parseStringify(user)
  } catch (error) {
    console.log(error)
    throw new Error("Error getting user")
  }
}

export const updateUser = async ({ user_id, data }: any) => {
  try {
    const { database } = await createAdminClient()

    const user = await database.updateDocument(
      APP_WRITE.databaseId,
      userCollection,
      user_id,
      data,
    )

    revalidateTag(`user-info-${user_id}`)

    return parseStringify(user)
  } catch (error) {
    console.log(error)
    throw new Error("Error updating user")
  }
}

export const updateUserWorkspace = async ({ user_id, workspaceSlug }: any) => {
  try {
    const { database } = await createAdminClient()

    const resp = await database.updateDocument(
      APP_WRITE.databaseId!,
      userCollection!,
      user_id!,
      {
        workspace: workspaceSlug,
      },
    )

    // setCookiesWorkspaces(resp)

    return resp
  } catch (error) {
    console.log(error)
    throw new Error("Error updating user workspace")
  }
}

// ------------------------------------------------------------------------------------
// AUTH

type TSignIn = {
  email: string
  password: string
}

export const signIn = async ({ email, password }: TSignIn) => {
  try {
    const { account } = await createAdminClient()
    const session = await account.createEmailPasswordSession(email, password)
    const cookiesStore = await cookies()

    cookiesStore.set(cookieName, session.secret, {
      path: "/",
      httpOnly: true,
      sameSite: "strict",
      secure: true,
    })

    const user = await getUserInfo({ user_id: session.userId })
    // setCookiesWorkspaces(user)

    return parseStringify(user)
  } catch (error) {
    console.error("Error", error)
    throw new Error("Invalid credentials. Please check the email and password.")
  }
}

export const signUp = async ({ password, ...userData }: any) => {
  const { email, username: name } = userData

  try {
    const { account } = await createAdminClient()

    await account.create(ID.unique(), email, password, name)

    const session = await account.createEmailPasswordSession(email, password)
    const cookiesStore = await cookies()

    cookiesStore.set(cookieName, session.secret, {
      path: "/",
      httpOnly: true,
      sameSite: "strict",
      secure: true,
    })

    const user = await getUserInfo({ user_id: session.userId })

    return parseStringify(user)
  } catch (error) {
    console.error("Error", error)
    throw new Error("Invalid credentials. Please check the information.")
  }
}

export async function getLoggedInUser() {
  try {
    const { account, teams } = await createSessionClient()
    if (!account) return null

    const result = await account.get()
    const teamResult = await teams.list()

    if (!result) return null
    const user = await getUserInfo({ user_id: result.$id })
    const currentPlan = teamResult?.teams?.[0]

    // setCookiesWorkspaces(user)
    const info = { ...user, team_plan: currentPlan }

    return parseStringify(info)
  } catch (error) {
    console.error(error)
    redirect(paths.auth.login)
  }
}

export const getUserDomains = async () => {
  try {
    const user = await getLoggedInUser()

    if (!user) return []

    const cachedDomains = unstable_cache(
      async () => {
        return await getDocuments({
          collectionId: APP_WRITE.collections.domains,
          queries: [
            Query.select(["$id", "subdomain", "domain_type"]),
            Query.equal("user_id", user.$id),
          ],
        })
      },
      ["domains", `domains-${user.$id}`],
      { tags: [`domains-${user.$id}`] },
    )

    const docs = await cachedDomains()
    return docs?.documents
  } catch (error) {
    console.error(error)
  }
}

export const logoutAccount = async () => {
  try {
    const { account } = await createSessionClient()
    if (!account) return null
    const cookiesStore = await cookies()

    cookiesStore.delete(cookieName)
    cookiesStore.delete(WORKSPACE_COOKIE_NAME)
    cookiesStore.delete(MY_WORKSPACES_COOKIE_NAME)

    await account.deleteSessions()
  } catch (error) {
    console.error("Error", error)
    throw new Error("Error logging out")
  }
}
