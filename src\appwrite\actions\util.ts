/* eslint-disable @typescript-eslint/no-unused-vars */
export const fDocument = (doc: any, excludeFields: string[] = []) => {
  const { $databaseId, $permissions, $collectionId, $createdAt, ...rest } = doc
  const filteredDoc = { ...rest }
  excludeFields.forEach((field) => {
    delete filteredDoc[field]
  })
  return {
    ...rest,
  }
}

export const fDocuments = (response: any, excludeFields: string[] = []) => {
  return {
    documents: response.documents.map((doc: any) => {
      return fDocument(doc, excludeFields)
    }),
  }
}
