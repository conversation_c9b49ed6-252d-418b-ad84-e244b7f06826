class OrmTracker {

  constructor(initialData) {
    this.originalData = JSON.parse(JSON.stringify(initialData)); // Deep copy to handle nested objects
    this.currentData = JSON.parse(JSON.stringify(initialData));
    this.changes = new Map();
    this.beforeSaveCallbacks = [];
    this.afterSaveCallbacks = [];

    return new Proxy(this, {
      get: (target, prop) => {
        if (prop in target) {
          return target[prop];
        } else {
          return target.currentData[prop];
        }
      },
      set: (target, prop, value) => {
        if (prop in target.currentData) {
          const originalValue = target.originalData[prop];
          const currentValue = target.currentData[prop];
          if (currentValue !== value) {
            target.changes.set(prop, [originalValue, value]);
            target.currentData[prop] = value;
          }
        } else {
          target.currentData[prop] = value;
        }
        return true;
      },
    });
  }

  isChanged() {
    return this.changes.size > 0;
  }

  attributeChanged(attrName) {
    return this.changes.has(attrName);
  }

  attributeWas(attrName) {
    return this.changes.has(attrName) ? this.changes.get(attrName)[0] : this.currentData[attrName];
  }

  getChanges() {
    const changesObj = {};
    for (let [key, value] of this.changes.entries()) {
      changesObj[key] = value;
    }
    return changesObj;
  }

  getChangedAttributes() {
    return Array.from(this.changes.keys());
  }

  attributeChangeToBeSaved(attrName) {
    return this.changes.get(attrName);
  }

  save() {
    this.runCallbacks(this.beforeSaveCallbacks);
    this.changes.clear();
    this.originalData = JSON.parse(JSON.stringify(this.currentData));
    this.runCallbacks(this.afterSaveCallbacks);
  }

  beforeSave(callback) {
    this.beforeSaveCallbacks.push(callback);
  }

  afterSave(callback) {
    this.afterSaveCallbacks.push(callback);
  }

  update(newData) {
    for (let [key, value] of Object.entries(newData)) {
      this[key] = value;
    }
  }

  runCallbacks(callbacks) {
    callbacks.forEach((callback) => callback(this));
  }
}

export default OrmTracker;

// // Example usage:
// const product = new OrmTracker({ name: 'Laptop', price: 1000 });

// product.beforeSave((obj) => {
//   console.log('Before save:', obj.getChanges());
// });

// product.afterSave((obj) => {
//   console.log('After save:', obj.currentData);
// });

// console.log(product.isChanged()); // false
// // product.price = 1200;

// const newProductData = { name: 'Laptop 21', price: 1200 };

// product.update(newProductData);

// console.log(product.isChanged()); // true
// console.log(product.getChanges()); // { price: [ 1000, 1200 ] }
// console.log(product.attributeChanged('price')); // true
// console.log(product.attributeWas('price')); // 1000
// console.log(product.price); // 1200

// // product.name = 'New Laptop';
// // console.log(product.getChangedAttributes()); // [ 'price', 'name' ]
// console.log(product.getChanges()); // { price: [ 1000, 1200 ], name: [ 'Laptop', 'New Laptop' ] }
// // product.save(); // Triggers beforeSave and afterSave callbacks
// // console.log(product.isChanged()); // false
// // console.log(product.getChanges()); // {}

// // product.name = 'Another Laptop';
// // console.log(product.attributeChangeToBeSaved('name')); // [ 'New Laptop', 'Another Laptop' ]