// https://sys.techtown.app/v1/storage/buckets/66df3b29001d77a945a3/files/66e6a6820027fb44fc74/view?project=easy-tech&mode=admin

import { APP_WRITE, twicpics } from "@/config-global"
import { PLACEHOLDER } from "@/modules/site-builder/const"
const { endPointUrl, bucketId, projectId } = APP_WRITE || {}

export const getFileUrl = (fileId: string, userBucketId?: string) => {
  return `${endPointUrl}/storage/buckets/${userBucketId || bucketId}/files/${fileId}/view?project=${projectId}`
}

export const isAppwriteStorage = (url: string) => {
  return url.includes(endPointUrl)
}

export const isExternalImage = (url: string) => {
  return !isAppwriteStorage(url)
}

export const getStoreIdFromUrl = (url: string) => {
  // If it's an external URL, return the original URL
  if (!isAppwriteStorage(url)) {
    return url
  }

  const parts = url.split("/")
  const fileIndex = parts.indexOf("files")

  if (fileIndex === -1 || fileIndex + 1 >= parts.length) {
    return url
  }

  const fileId = parts[fileIndex + 1]
  return fileId
}

export const getAdaptiveImageUrl = (fileId?: string | null): string | null => {
  if (!fileId) return PLACEHOLDER

  // If it's not from Appwrite storage, return the original URL
  if (isExternalImage(fileId)) {
    return fileId
  }

  const appwriteStorage = `${endPointUrl}/storage/buckets`
  const twicPicsDomain = twicpics.domain

  return fileId.replace(appwriteStorage, twicPicsDomain)
}
