export type CrudOperators =
  | 'eq' // Equal
  | 'ne' // Not equal
  | 'lt' // Less than
  | 'gt' // Greater than
  | 'lte' // Less than or equal to
  | 'gte' // Greater than or equal to
  // | 'in' // Included in an array
  // | 'nin' // Not included in an array
  | 'contains' // Contains
  | 'ncontains' // Doesn't contain
  // | 'containss' // Contains, case sensitive
  // | 'ncontainss' // Doesn't contain, case sensitive
  | 'between' // Between
  // | 'nbetween' // Not between
  | 'null' // Is null
  | 'nnull' // Is not null
  | 'startswith' // Starts with
  | 'nstartswith' // Doesn't start with
  // | 'startswiths' // Starts with, case sensitive
  // | 'nstartswiths' // Doesn't start with, case sensitive
  | 'endswith' // Ends with
  | 'nendswith' // Doesn't end with
  // | 'endswiths' // Ends with, case sensitive
  // | 'nendswiths' // Doesn't end with, case sensitive
  | 'or' // Logical OR
  | 'and' // Logical AND
  | 'search'; // Search

export type LogicalFilter = {
  field: string;
  operator: Exclude<CrudOperators, 'or' | 'and'>;
  value: any;
};

export type ConditionalFilter = {
  key?: string;
  operator: Extract<CrudOperators, 'or' | 'and'>;
  value: (LogicalFilter | ConditionalFilter)[];
};

export type CrudFilter = LogicalFilter | ConditionalFilter;

export type CrudFilters = CrudFilter[];

export type CrudSort = {
  field: string;
  order:
    | 'asc' // Ascending order
    | 'desc'; // Descending order
};

export type CrudSorting = CrudSort[];

export type Pagination = {
  current?: number; // Initial page index
  pageSize?: number; // Initial number of items per page
  mode?: 'client' | 'server' | 'off'; // Whether to use server side pagination or not.
};

export type BaseKey = string | number;

export type BaseRecord = {
  id?: BaseKey;
  [key: string]: any;
};

export type QueryBuilderOptions = {
  operation?: string;
  fields?: Array<string | object | NestedField>;
  variables?: VariableOptions;
};

export type NestedField = {
  operation: string;
  variables: QueryBuilderOptions[];
  fields: Array<string | object | NestedField>;
};

export type VariableOptions = {
  type?: string;
  name?: string;
  value: any;
  list?: boolean;
  required?: boolean;
  [key: string]: any;
};

export type SyncWithLocationParams = {
  pagination: { current?: number; pageSize?: number };
  sorters: CrudSorting;
  filters: CrudFilters;
};
