'use client';
export { ID } from 'appwrite';

import { APP_WRITE } from '@/config-global';

import { Client, Account } from 'appwrite';

const client = new Client();

client.setEndpoint(APP_WRITE.endPointUrl).setProject(APP_WRITE.projectId);

// @ts-ignore
const account = new Account(client);

const resources = {
  databaseId: 'techtown-crm',
  users: 'users',

  lists: 'lists',
  listViews: 'list-views',
  listAttributes: 'list-attributes',

  objects: 'objects',
  objectAttributes: 'object-attributes',

  workflows: 'workflows',
  workflowSteps: 'workflow-steps',

  znsTemplates: 'zns-templates',

  // OLD
  contacts: 'contacts',
  companies: 'companies',
  quotes: 'quotes',

  //
  events: '660e290f000d7cbcc7e8',
  eventCategories: '660e28ca00396db78f65',

  //
  faqs: '660e379d0015c06a035a',
} as const;

const resourceMetadata = [
  {
    name: 'contacts',
    list: '/contact',
    show: '/contact/:id',
    create: '/contact/create',
    edit: '/contact/edit/:id',
    meta: {
      canDelete: true,
    },
  },

  {
    name: 'companies',
    list: '/company',
    show: '/company/:id',
    create: '/company/create',
    edit: '/company/edit/:id',
    meta: {
      canDelete: true,
    },
  },
];

export { client, account, resources, resourceMetadata };
