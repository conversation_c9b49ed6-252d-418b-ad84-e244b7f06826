import { CrudSort, LogicalFilter, Pagination } from '../types/query';
import { getAppwriteFilters } from './get-appwrite-filters';
import { getAppwriteSorting } from './get-appwrite-sorting';
import { getAppwritePagination } from './get-appwrite-pagination';

type AppwriteQuery = {
  filters?: LogicalFilter[];
  sorts?: CrudSort[];
  pagination?: Pagination;
};

// ------------------------------------------------------------------------------------------------

export const buildQueries = ({ filters, sorts, pagination }: AppwriteQuery): any => {
  const queries = [];

  // Filters
  if (filters) {
    const generatedFilters = getAppwriteFilters(filters);
    queries.push(...generatedFilters);
  }

  // Sorts
  if (sorts) {
    const generatedSorts = getAppwriteSorting(sorts);
    queries.push(...generatedSorts);
  }

  // Pagination
  if (pagination) {
    const paginationQueries = getAppwritePagination(
      Number(pagination?.current),
      Number(pagination?.pageSize)
    );
    queries.push(...paginationQueries);
  }

  return queries;
};
