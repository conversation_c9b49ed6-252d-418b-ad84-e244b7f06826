import { Query } from 'node-appwrite';
import { CrudFilter } from '../types/query';

export const generateFilter = (filter: CrudFilter) => {
  switch (filter.operator) {
    case 'eq':
      return Query.equal(filter.field, filter.value);
    case 'ne':
      return Query.notEqual(filter.field, filter.value);
    case 'lt':
      return Query.lessThan(filter.field, filter.value);
    case 'gt':
      return Query.greaterThan(filter.field, filter.value);
    case 'lte':
      return Query.lessThanEqual(filter.field, filter.value);
    case 'gte':
      return Query.greaterThanEqual(filter.field, filter.value);
    case 'contains':
      return Query.search(filter.field, `%${filter.value}%`);
    case 'ncontains':
      return Query.search(filter.field, `!%${filter.value}%`);
    case 'between':
      return Query.between(filter.field, filter.value[0], filter.value[1]);
    case 'null':
      return Query.isNull(filter.field);
    case 'nnull':
      return Query.isNotNull(filter.field);
    case 'startswith':
      return Query.search(filter.field, `${filter.value}%`);
    case 'nstartswith':
      return Query.search(filter.field, `!${filter.value}%`);
    case 'endswith':
      return Query.search(filter.field, `%${filter.value}`);
    case 'nendswith':
      return Query.search(filter.field, `%!${filter.value}`);

    //
    default:
      throw new Error(`Operator ${filter.operator} is not supported`);
  }
};
