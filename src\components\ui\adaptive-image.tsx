"use client"

import { TwicPicture } from "@twicpics/components/react"
import { isAppwriteStorage, getAdaptiveImageUrl } from "@/appwrite/storage"
import ExternalUrlImage from "./external-url-image"

type TwicPictureProps = Omit<React.ComponentProps<typeof TwicPicture>, "src">

interface AdaptiveImageProps extends TwicPictureProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  style?: React.CSSProperties
  onClick?: () => void
  loading?: "lazy" | "eager"
  aspectRatio?: string
}

/**
 * Adaptive Image component that conditionally renders:
 * - TwicPicture for Appwrite storage images (optimized)
 * - ExternalUrlImage for external URLs
 */
const AdaptiveImage = ({
  src,
  alt,
  className,
  width,
  height,
  style,
  onClick,
  loading = "lazy",
  aspectRatio,
  // Spread all other TwicPicture props
  ...twicPictureProps
}: AdaptiveImageProps) => {
  // Check if it's Appwrite storage URL
  const isAppwriteImage = isAppwriteStorage(src)

  if (isAppwriteImage) {
    // Use TwicPicture for Appwrite images (with optimization)
    const twicPicsUrl = getAdaptiveImageUrl(src)

    const twicPictureElement = (
      <TwicPicture
        src={twicPicsUrl || src}
        alt={alt}
        className={className}
        style={style}
        eager={twicPictureProps.eager || loading === "eager"}
        {...twicPictureProps}
      />
    )

    if (onClick) {
      return (
        <div onClick={onClick} className="cursor-pointer">
          {twicPictureElement}
        </div>
      )
    }

    return twicPictureElement
  }

  // Use ExternalUrlImage for external URLs (like Unsplash)
  return (
    <ExternalUrlImage
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      style={style}
      onClick={onClick}
      loading={loading}
      aspectRatio={aspectRatio}
    />
  )
}

export default AdaptiveImage
