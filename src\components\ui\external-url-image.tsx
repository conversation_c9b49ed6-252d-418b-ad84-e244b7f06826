"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { cn } from "@/utils"

interface ExternalUrlImageProps {
  src: string
  alt: string
  className?: string
  width?: number
  height?: number
  style?: React.CSSProperties
  onClick?: () => void
  loading?: "lazy" | "eager"
  aspectRatio?: string
}

const ExternalUrlImage = ({
  src,
  alt,
  className,
  width,
  height,
  style,
  onClick,
  loading = "lazy",
  aspectRatio,
}: ExternalUrlImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(loading === "eager")
  const [hasError, setHasError] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  const handleLoad = useCallback(() => {
    setIsLoaded(true)
  }, [])

  const handleError = useCallback(() => {
    setHasError(true)
  }, [])

  useEffect(() => {
    const imgElement = imgRef.current
    if (!imgElement || loading === "eager") {
      setIsInView(true)
      return
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.unobserve(imgElement)
        }
      },
      {
        threshold: 0.01,
        rootMargin: "100px",
      },
    )

    observer.observe(imgElement)

    return () => {
      if (imgElement) {
        observer.unobserve(imgElement)
      }
    }
  }, [loading])

  useEffect(() => {
    if (imgRef.current?.complete) {
      setIsLoaded(true)
      setIsInView(true)
    }
  }, [])

  return (
    <div
      className={cn("relative overflow-hidden", className)}
      style={{
        ...style,
        aspectRatio:
          aspectRatio || (width && height ? `${width}/${height}` : undefined),
      }}
      onClick={onClick}
    >
      {!isLoaded && !hasError && isInView && (
        <div className="absolute inset-0 animate-pulse bg-gray-200" />
      )}

      {isInView && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          width={width}
          height={height}
          className={cn(
            "h-full w-full object-cover",
            isLoaded ? "opacity-100" : "opacity-0",
            "transition-opacity duration-200 ease-out",
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading={loading}
          decoding="async"
        />
      )}
    </div>
  )
}

export default ExternalUrlImage
