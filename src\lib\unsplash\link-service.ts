import { T_UserMedia } from "@/lib/appwrite-actions/admin/user-media.action"
import { createUserLinkEntity } from "@/lib/appwrite-actions/admin/user-link-entity.action"
import { getLoggedInUser } from "@/appwrite/actions/user.actions"
import { UnsplashImage } from "./index"
import { UNSPLASH_CONFIG } from "@/config-global"

export const unsplashLinkService = {
  /**
   * Save Unsplash image link without downloading the actual image
   */
  async saveUnsplashImageLink(
    unsplashImage: UnsplashImage,
  ): Promise<T_UserMedia> {
    try {
      const user = await getLoggedInUser()

      if (!user) {
        throw new Error("Authentication required")
      }

      fetch(`${UNSPLASH_CONFIG.apiUrl}/photos/${unsplashImage.id}/download`, {
        headers: {
          Authorization: `Client-ID ${UNSPLASH_CONFIG.accessApiKey}`,
        },
      }).catch((error) => {
        console.warn(
          `Failed to track download for image ${unsplashImage.id}:`,
          error,
        )
      })

      // Create a user media entry with Unsplash link
      const response = await createUserLinkEntity({
        user_id: user.$id,
        data: {
          user_id: user.$id,
          type: "COLLECTION",
          entity_id: unsplashImage.id,
          value: unsplashImage.urls.regular, // Use regular size as default
          value_type: "unsplash_url",
          name:
            unsplashImage.alt_description ||
            `Unsplash photo by ${unsplashImage.user.name}`,
          metadata: JSON.stringify({
            unsplash: {
              id: unsplashImage.id,
              alt_description: unsplashImage.alt_description,
              description: unsplashImage.description,
              user: {
                name: unsplashImage.user.name,
                username: unsplashImage.user.username,
              },
              urls: unsplashImage.urls,
              width: unsplashImage.width,
              height: unsplashImage.height,
            },
          }),
        },
      })

      // Format the response to match T_UserMedia structure
      const userMedia: T_UserMedia = {
        ...response,
        src: unsplashImage.urls.regular,
        alt:
          unsplashImage.alt_description ||
          `Photo by ${unsplashImage.user.name}`,
        title: unsplashImage.description || unsplashImage.alt_description,
        thumbnail: unsplashImage.urls.thumb,
      }

      return userMedia
    } catch (error) {
      console.error("Error saving Unsplash image link:", error)
      throw new Error(
        `Failed to save image link: ${error instanceof Error ? error.message : "Unknown error"}`,
      )
    }
  },

  /**
   * Batch save multiple Unsplash image links
   */
  async saveMultipleUnsplashImageLinks(
    unsplashImages: UnsplashImage[],
  ): Promise<T_UserMedia[]> {
    const results: T_UserMedia[] = []
    const errors: string[] = []

    for (const unsplashImage of unsplashImages) {
      try {
        const savedImage = await this.saveUnsplashImageLink(unsplashImage)
        results.push(savedImage)
      } catch (error) {
        console.error(`Failed to save image link ${unsplashImage.id}:`, error)
        errors.push(
          `${unsplashImage.id}: ${error instanceof Error ? error.message : "Unknown error"}`,
        )
      }
    }

    if (errors.length > 0) {
      console.warn("Some image links failed to save:", errors)
    }

    return results
  },

  /**
   * Get Unsplash image URL by size preference
   */
  getOptimalImageUrl(
    unsplashImage: UnsplashImage,
    size: "thumb" | "small" | "regular" | "full" = "regular",
  ): string {
    return unsplashImage.urls[size] || unsplashImage.urls.regular
  },
}
