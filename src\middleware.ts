import { cookies } from "next/headers"
import { NextRequest, NextResponse } from "next/server"
import { createSessionClient } from "./appwrite/appwrite"
import {
  allowDomains,
  appApiUrl,
  cookieName,
  rootDomain,
} from "./config-global"
import { paths } from "./routes/paths"

// Language detection utilities
async function getGeoData() {
  try {
    const response = await fetch("https://ip-api.com/json/")
    const data = await response.json()
    return { country: data.countryCode || "US" }
  } catch (error) {
    console.error("Failed to fetch geo data:", error)
    return { country: "US" }
  }
}

function getLanguageFromCountry(countryCode: string): string {
  const countryToLanguage: Record<string, string> = {
    VN: "vi",
    US: "en",
    GB: "en",
    AU: "en",
    CA: "en",
    JP: "ja",
    KR: "ko",
    TH: "th",
    CN: "zh",
    TW: "zh",
    HK: "zh",
    FR: "fr",
    DE: "de",
    ES: "es",
    IT: "it",
    RU: "ru",
    BR: "pt",
    PT: "pt",
    IN: "hi",
    ID: "id",
    MY: "ms",
    SG: "en",
    PH: "en",
  }

  return countryToLanguage[countryCode] || "en"
}

// 1. Specify protected and public routes
const protectedRoutes = ["/platform"]
const publicRoutes = ["/auth"]

export async function middleware(req: NextRequest) {
  const url = req.nextUrl

  // 2. Check if the current route is protected or public
  const pathname = req.nextUrl.pathname
  const isProtectedRoute = protectedRoutes.includes(pathname)
  const isPublicRoute = publicRoutes.includes(pathname)

  // Get hostname of request (e.g. demo.vercel.pub, demo.localhost:1000)
  const hostname = req.headers
    .get("host")!
    .replace(".localhost:8000", `.${rootDomain}`)

  const searchParams = req.nextUrl.searchParams.toString()
  // Get the pathname of the request (e.g. /, /about, /blog/first-post)
  const path = `${url.pathname}${
    searchParams.length > 0 ? `?${searchParams}` : ""
  }`

  // Language detection logic
  const cookieStore = await cookies()
  const savedLanguage = cookieStore.get("language")?.value

  let detectedLanguage = "en"
  if (!savedLanguage) {
    let country = "US"

    if (hostname.includes("localhost")) {
      // Localhost: call real IP API
      const geoData = await getGeoData()
      country = geoData.country
    } else {
      // Production: use Vercel headers
      country =
        req.geo?.country || req.headers.get("x-vercel-ip-country") || "US"
    }

    detectedLanguage = getLanguageFromCountry(country)
  } else {
    detectedLanguage = savedLanguage
  }

  // 3. Decrypt the session from the cookie
  const session = cookieStore.get(cookieName)?.value

  // 4. Redirect to /login if the user is not authenticated
  if (isProtectedRoute && !session) {
    const response = NextResponse.redirect(
      new URL(paths.auth.login, req.nextUrl),
    )
    if (!savedLanguage) {
      response.cookies.set("language", detectedLanguage, {
        maxAge: 60 * 60 * 24 * 30,
        path: "/",
        sameSite: "lax",
        secure: process.env.NODE_ENV === "production",
      })
    }
    return response
  }

  const { account } = await createSessionClient()

  // 5. Redirect to / if the user is authenticated and the route is auth
  if (isPublicRoute && account && pathname.startsWith("/auth")) {
    const response = NextResponse.redirect(new URL("/", req.nextUrl))
    if (!savedLanguage) {
      response.cookies.set("language", detectedLanguage, {
        maxAge: 60 * 60 * 24 * 30,
        path: "/",
        sameSite: "lax",
        secure: process.env.NODE_ENV === "production",
      })
    }
    return response
  }

  const isRootSubdomains = allowDomains.includes(hostname)
  const isRootDomain = hostname === rootDomain

  // const currentHost = hostname
  // .replace(".crefion.com", "")
  // .replace(".localhost:8000", "")

  // Avoid rewriting for the root domain
  if (hostname !== "app.crefion.com" && hostname) {
    // const siteData = await getSiteData(hostname)
    // const websiteId = siteData?.site?.$id

    const siteData = await fetch(
      `${appApiUrl}/api/builder/sites?domain=${hostname}`,
    )

    const websiteId = (await siteData.json())?.siteId
    if (!websiteId) {
      const response = NextResponse.next()
      if (!savedLanguage) {
        response.cookies.set("language", detectedLanguage, {
          maxAge: 60 * 60 * 24 * 30,
          path: "/",
          sameSite: "lax",
          secure: process.env.NODE_ENV === "production",
        })
      }
      response.headers.set("x-detected-language", detectedLanguage)
      return response
    }

    url.pathname = `/websites/${websiteId}${pathname}`

    const response = NextResponse.rewrite(url)
    if (!savedLanguage) {
      response.cookies.set("language", detectedLanguage, {
        maxAge: 60 * 60 * 24 * 30,
        path: "/",
        sameSite: "lax",
        secure: process.env.NODE_ENV === "production",
      })
    }
    response.headers.set("x-detected-language", detectedLanguage)
    return response
  }

  // Skip rewrite for s3 subdomain
  if (hostname.startsWith("s3.")) {
    const response = NextResponse.next()
    if (!savedLanguage) {
      response.cookies.set("language", detectedLanguage, {
        maxAge: 60 * 60 * 24 * 30,
        path: "/",
        sameSite: "lax",
        secure: process.env.NODE_ENV === "production",
      })
    }
    response.headers.set("x-detected-language", detectedLanguage)
    return response
  }

  const rewritePath =
    isRootDomain || isRootSubdomains
      ? `/platform${path === "/" ? "" : path}`
      : `/${hostname}${path}`

  const response = NextResponse.rewrite(new URL(rewritePath, req.url))

  // Set language cookie if not already set
  if (!savedLanguage) {
    response.cookies.set("language", detectedLanguage, {
      maxAge: 60 * 60 * 24 * 30, // 30 days
      path: "/",
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
    })
  }

  // Add language header for components to use
  response.headers.set("x-detected-language", detectedLanguage)

  return response
}

export const config = {
  matcher: [
    "/((?!api/|_next/|_static/|_vercel|.well-known|images|assets|auth|components|[\\w-]+\\.\\w+).*)",
  ],
}
