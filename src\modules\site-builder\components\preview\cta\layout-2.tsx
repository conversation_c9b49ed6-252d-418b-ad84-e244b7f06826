"use client"
import {
  E_ContentAlignment,
  E_TextAlignment,
} from "@/modules/site-builder/enums"
import { I_Section } from "@/types/app/website"
import { T_CTAFields, T_SettingLayout } from "@/types/app/website-section"

import { cn } from "@/utils"
import {
  getAlignSelfByContentAlignment,
  getClassWidthSetting,
  getFlexJustifyByTextAlignment,
  getStyleHeightSetting,
  getTextJustifyByTextAlignment,
} from "@/modules/site-builder/utils/class-style"
import { getStyleByPalette } from "@/modules/site-builder/utils/palette"

import ImageTemplate from "@/modules/site-builder/templates/image"
import BlockPreview from "../components/block"
import {
  SiteBodyTemplate,
  SiteButtonDialogTemplate,
  SiteButtonTemplate,
  SiteHeadingTemplate,
} from "@/modules/site-builder/templates"
import { useLightbox } from "@/providers/lightbox-ui"
import { T_UserMedia } from "@/lib/appwrite-actions/admin/user-media.action"
import { CroppedImgComponent } from "../../common"
import { getAdaptiveImageUrl } from "@/appwrite/storage"

interface IProps {
  selected: boolean
  data: I_Section
  isLiveView?: boolean
  onClick?: () => void
  colors: string[]
}

const CTAPreviewLayout2 = ({
  selected,
  data,
  isLiveView,
  onClick,
  colors,
}: IProps) => {
  const { openLightbox } = useLightbox()
  const fields = JSON.parse(data.fields) as T_CTAFields
  const {
    media,
    heading,
    primary_button,
    secondary_button,
    palette,
    background_image,
    content_alignment,
    text_alignment,
    padding_bottom,
    padding_top,
    padding_left,
    padding_right,
    content,
    setting_layout,
    box_shadow,
  } = fields

  const bgUrl = background_image?.active ? background_image?.src : ""
  const mediaSrc = media?.active ? media?.src : ""

  const styleHeight = getStyleHeightSetting(setting_layout as T_SettingLayout)
  const classWithSetting = getClassWidthSetting(setting_layout)

  const styleByPalette = getStyleByPalette(palette, colors)

  const contentAlignment = getAlignSelfByContentAlignment(
    content_alignment as E_ContentAlignment,
  )

  const textAlignment = getTextJustifyByTextAlignment(
    text_alignment as E_TextAlignment,
  )

  const textFlexJustify = getFlexJustifyByTextAlignment(
    text_alignment as E_TextAlignment,
  )

  return (
    <BlockPreview
      selected={selected}
      isLiveView={isLiveView}
      labelSelected="Edit CTA Content"
    >
      <section
        onClick={onClick}
        className={cn(
          "relative z-20 flex w-full items-center justify-center overflow-hidden",
          contentAlignment,
          textAlignment,
        )}
        style={{
          ...styleByPalette,
          ...styleHeight,

          ...(bgUrl && {
            backgroundImage: `url(${getAdaptiveImageUrl(bgUrl) || ""})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            aspectRatio: "16/9",
            borderRadius: `0px`,
          }),
        }}
      >
        <div
          className={cn(
            "grid h-full grid-cols-1 gap-4 lg:grid-cols-2",
            classWithSetting,
            content_alignment === E_ContentAlignment.CENTER && "items-center",
            content_alignment === E_ContentAlignment.TOP && "items-start",
            content_alignment === E_ContentAlignment.BOTTOM && "items-end",
          )}
          style={{
            paddingTop: `${padding_top}px`,
            paddingBottom: `${padding_bottom}px`,
            paddingLeft: `${padding_left}px`,
            paddingRight: `${padding_right}px`,
          }}
        >
          {/* IMAGE SECTION - Left side */}
          {media && media.active === true && (
            <div
              className={cn(
                "flex h-full w-full items-center justify-center",
                content_alignment === E_ContentAlignment.CENTER &&
                  "items-center",
                content_alignment === E_ContentAlignment.TOP && "items-start",
                content_alignment === E_ContentAlignment.BOTTOM && "items-end",
              )}
            >
              {media.crop_aspect && media.crop_active ? (
                <CroppedImgComponent
                  media={media as T_UserMedia}
                  // openLightbox={() => openLightbox(null, media as T_UserMedia)}
                />
              ) : (
                <div
                  onClick={() => openLightbox(null, media as T_UserMedia)}
                  className={cn("w-full cursor-pointer")}
                >
                  <ImageTemplate src={mediaSrc || ""} radius={media?.radius} />
                </div>
              )}
            </div>
          )}

          {/* CONTENT SECTION - Right side */}
          <div
            className={cn(
              "relative flex flex-col items-center gap-2 sm:gap-4 lg:gap-6",
            )}
          >
            {heading && heading.active === true && (
              <SiteHeadingTemplate
                colors={colors}
                text={heading?.text}
                size={heading?.size}
              />
            )}
            {content && content.active === true && content.text && (
              <SiteBodyTemplate
                text={content.text}
                className={cn(
                  "max-w-screen-md text-end text-sm",
                  text_alignment === E_TextAlignment.CENTER && "mx-auto",
                  textAlignment,
                  textFlexJustify,
                )}
              />
            )}
            <div className={cn("flex w-full gap-4", textFlexJustify)}>
              {primary_button && primary_button.active === true && (
                <SiteButtonDialogTemplate
                  variant="primary"
                  palette={palette}
                  colors={colors}
                  buttonAction={primary_button.text}
                  content={<div>THIS IS A CONTENT</div>}
                  icon={primary_button.icon}
                  boxShadow={box_shadow}
                />
              )}

              {secondary_button && secondary_button.active === true && (
                <SiteButtonTemplate
                  palette={palette}
                  colors={colors}
                  variant="secondary"
                  text={secondary_button.text}
                  href={secondary_button.url}
                  openInNewTab={secondary_button.open_in_new_tab}
                  icon={secondary_button.icon}
                  isLiveView={isLiveView}
                  boxShadow={box_shadow}
                />
              )}
            </div>
          </div>
        </div>
      </section>
    </BlockPreview>
  )
}

export default CTAPreviewLayout2
