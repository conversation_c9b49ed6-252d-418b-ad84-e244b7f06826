"use client"
import {
  E_ContentAlignment,
  E_TextAlignment,
} from "@/modules/site-builder/enums"
import { I_Section } from "@/types/app/website"
import { T_CTAFields, T_SettingLayout } from "@/types/app/website-section"

import { cn } from "@/utils"
import {
  getAlignSelfByContentAlignment,
  getClassWidthSetting,
  getFlexJustifyByTextAlignment,
  getStyleHeightSetting,
  getTextJustifyByTextAlignment,
} from "@/modules/site-builder/utils/class-style"
import { getStyleByPalette } from "@/modules/site-builder/utils/palette"

import BlockPreview from "../components/block"
import {
  SiteBodyTemplate,
  SiteButtonDialogTemplate,
  SiteButtonTemplate,
  SiteHeadingTemplate,
} from "@/modules/site-builder/templates"
import { getAdaptiveImageUrl } from "@/appwrite/storage"

interface IProps {
  selected: boolean
  data: I_Section
  isLiveView?: boolean
  onClick?: () => void
  colors: string[]
}

const CTAPreviewLayout3 = ({
  selected,
  data,
  isLiveView,
  onClick,
  colors,
}: IProps) => {
  const fields = JSON.parse(data.fields) as T_CTAFields
  const {
    heading,
    primary_button,
    secondary_button,
    palette,
    background_image,
    content_alignment,
    text_alignment,
    padding_bottom,
    padding_top,
    padding_left,
    padding_right,
    content,
    setting_layout,
    box_shadow,
  } = fields

  const bgUrl = background_image?.active ? background_image?.src : ""

  const styleHeight = getStyleHeightSetting(setting_layout as T_SettingLayout)
  const classWithSetting = getClassWidthSetting(setting_layout)

  const styleByPalette = getStyleByPalette(palette, colors)

  const contentAlignment = getAlignSelfByContentAlignment(
    content_alignment as E_ContentAlignment,
  )

  const textAlignment = getTextJustifyByTextAlignment(
    text_alignment as E_TextAlignment,
  )

  const textFlexJustify = getFlexJustifyByTextAlignment(
    text_alignment as E_TextAlignment,
  )

  return (
    <BlockPreview
      selected={selected}
      isLiveView={isLiveView}
      labelSelected="Edit CTA Content"
    >
      <div
        onClick={onClick}
        className={cn(
          "cta-layout-3 flex w-full text-center",
          getTextJustifyByTextAlignment(text_alignment as E_TextAlignment),
        )}
        style={{
          paddingTop: padding_top ? `${padding_top}px` : undefined,
          paddingRight: padding_right ? `${padding_right}px` : undefined,
          paddingBottom: padding_bottom ? `${padding_bottom}px` : undefined,
          paddingLeft: padding_left ? `${padding_left}px` : undefined,
          backgroundImage: `url(${getAdaptiveImageUrl(bgUrl) || ""})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          ...styleByPalette,
          ...styleHeight,
        }}
      >
        <div
          className={cn(
            "mx-auto flex flex-col gap-2 sm:gap-4 lg:gap-6",
            contentAlignment,
            classWithSetting,
          )}
        >
          {heading && heading.active === true && (
            <SiteHeadingTemplate
              colors={colors}
              text={heading?.text}
              size={heading?.size}
            />
          )}
          {content && content.active === true && content.text && (
            <SiteBodyTemplate
              text={content.text}
              className={cn(
                "mt-4 w-full max-w-screen-md text-sm",
                text_alignment === E_TextAlignment.CENTER && "mx-auto",
                text_alignment === E_TextAlignment.RIGHT && "ml-auto",
                textAlignment,
                textFlexJustify,
              )}
            />
          )}
          <div className={cn("flex w-full gap-4", textFlexJustify)}>
            {primary_button && primary_button.active === true && (
              <SiteButtonDialogTemplate
                variant="primary"
                palette={palette}
                colors={colors}
                buttonAction={primary_button.text}
                content={<div>THIS IS A CONTENT</div>}
                icon={primary_button.icon}
                boxShadow={box_shadow}
              />
            )}

            {secondary_button && secondary_button.active === true && (
              <SiteButtonTemplate
                palette={palette}
                colors={colors}
                variant="secondary"
                text={secondary_button.text}
                href={secondary_button.url}
                openInNewTab={secondary_button.open_in_new_tab}
                icon={secondary_button.icon}
                isLiveView={isLiveView}
                boxShadow={box_shadow}
              />
            )}
          </div>
        </div>
      </div>
    </BlockPreview>
  )
}

export default CTAPreviewLayout3
