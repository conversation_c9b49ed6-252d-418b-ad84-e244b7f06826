"use client"
import {
  E_ContentAlignment,
  E_TextAlignment,
} from "@/modules/site-builder/enums"
import { I_Section } from "@/types/app/website"
import { T_CTAFields, T_SettingLayout } from "@/types/app/website-section"

import { cn } from "@/utils"
import {
  getAlignSelfByContentAlignment,
  getClassWidthSetting,
  getFlexJustifyByTextAlignment,
  getStyleHeightSetting,
  getTextJustifyByTextAlignment,
} from "@/modules/site-builder/utils/class-style"
import { getStyleByPalette } from "@/modules/site-builder/utils/palette"

import BlockPreview from "../components/block"
import {
  SiteBodyTemplate,
  SiteButtonDialogTemplate,
  SiteButtonTemplate,
  SiteHeadingTemplate,
} from "@/modules/site-builder/templates"
import { useLightbox } from "@/providers/lightbox-ui"
import { T_UserMedia } from "@/lib/appwrite-actions/admin/user-media.action"
import { CroppedImgComponent } from "../../common"
import { getAdaptiveImageUrl } from "@/appwrite/storage"
import AdaptiveImage from "@/components/ui/adaptive-image"

interface IProps {
  selected: boolean
  data: I_Section
  isLiveView?: boolean
  onClick?: () => void
  colors: string[]
}

const CTAPreviewLayout5 = ({
  selected,
  data,
  isLiveView,
  onClick,
  colors,
}: IProps) => {
  const { openLightbox } = useLightbox()
  const fields = JSON.parse(data.fields) as T_CTAFields
  const {
    media,
    heading,
    primary_button,
    secondary_button,
    palette,
    background_image,
    content_alignment,
    text_alignment,
    padding_bottom,
    padding_top,
    padding_left,
    padding_right,
    content,
    setting_layout,
    box_shadow,
  } = fields

  const bgUrl = background_image?.active ? background_image?.src : ""
  const mediaSrc = media?.active ? media?.src : ""

  const styleHeight = getStyleHeightSetting(setting_layout as T_SettingLayout)
  const classWithSetting = getClassWidthSetting(setting_layout)

  const styleByPalette = getStyleByPalette(palette, colors)

  const contentAlignment = getAlignSelfByContentAlignment(
    content_alignment as E_ContentAlignment,
  )

  const textAlignment = getTextJustifyByTextAlignment(
    text_alignment as E_TextAlignment,
  )

  const textFlexJustify = getFlexJustifyByTextAlignment(
    text_alignment as E_TextAlignment,
  )

  return (
    <BlockPreview
      selected={selected}
      isLiveView={isLiveView}
      labelSelected="Edit CTA Content"
    >
      <div
        onClick={onClick}
        className={cn(
          "cta-layout-5 flex w-full text-center",
          getTextJustifyByTextAlignment(text_alignment as E_TextAlignment),
        )}
        style={{
          paddingTop: padding_top ? `${padding_top}px` : undefined,
          paddingRight: padding_right ? `${padding_right}px` : undefined,
          paddingBottom: padding_bottom ? `${padding_bottom}px` : undefined,
          paddingLeft: padding_left ? `${padding_left}px` : undefined,
          ...(bgUrl && {
            backgroundImage: `url(${getAdaptiveImageUrl(bgUrl) || ""})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            aspectRatio: "16/9",
            borderRadius: `0px`,
          }),
          ...styleByPalette,
          ...styleHeight,
        }}
      >
        <div
          className={cn(
            "flex flex-col items-center justify-center gap-2 sm:gap-4 lg:gap-6",
            contentAlignment,
            classWithSetting,
          )}
        >
          {/* IMAGE SECTION - Top */}
          {media &&
            media.active === true &&
            (media.crop_aspect && media.crop_active ? (
              <CroppedImgComponent
                className="my-6 flex h-full w-full max-w-screen-lg items-center justify-center overflow-hidden"
                media={media}
                openLightbox={() => openLightbox(null, media as T_UserMedia)}
                imageRadius={media?.radius ? `${media.radius}px` : undefined}
              />
            ) : (
              <div
                onClick={() => openLightbox(null, media as T_UserMedia)}
                className="my-6 flex h-full max-h-[30vh] w-full max-w-screen-lg overflow-hidden"
                style={{
                  borderRadius: media?.radius ? `${media.radius}px` : "0px",
                }}
              >
                <AdaptiveImage
                  mode="cover"
                  src={getAdaptiveImageUrl(mediaSrc) || ""}
                  alt=""
                  sizes="(min-width: 1536px) 768px, (min-width: 1280px) 640px, (min-width: 1024px) 512px, (min-width: 768px) 384px, (min-width: 640px) 320px, 100vw"
                  className="origin-top-left"
                  ratio={"none"}
                />
              </div>
            ))}

          {/* CONTENT SECTION - Bottom */}
          {heading && heading.active === true && (
            <SiteHeadingTemplate
              colors={colors}
              text={heading?.text}
              size={heading?.size}
            />
          )}
          {content && content.active === true && content.text && (
            <SiteBodyTemplate
              text={content.text}
              className={cn(
                "mt-4 max-w-screen-md text-sm",
                text_alignment === E_TextAlignment.CENTER && "mx-auto",
                textAlignment,
                textFlexJustify,
              )}
            />
          )}
          <div className={cn("flex w-full gap-4", textFlexJustify)}>
            {primary_button && primary_button.active === true && (
              <SiteButtonDialogTemplate
                variant="primary"
                palette={palette}
                colors={colors}
                buttonAction={primary_button.text}
                content={<div>THIS IS A CONTENT</div>}
                icon={primary_button.icon}
                boxShadow={box_shadow}
              />
            )}

            {secondary_button && secondary_button.active === true && (
              <SiteButtonTemplate
                palette={palette}
                colors={colors}
                variant="secondary"
                text={secondary_button.text}
                href={secondary_button.url}
                openInNewTab={secondary_button.open_in_new_tab}
                icon={secondary_button.icon}
                isLiveView={isLiveView}
                boxShadow={box_shadow}
              />
            )}
          </div>
        </div>
      </div>
    </BlockPreview>
  )
}

export default CTAPreviewLayout5
