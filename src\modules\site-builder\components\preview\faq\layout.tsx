"use client"

import BlockPreview from "../components/block"
import { I_Section } from "@/types/app/website"
import { T_FAQFields, T_SettingLayout } from "@/types/app/website-section"
import { getStyleByPalette } from "@/modules/site-builder/utils/palette"
import { getStyleHeightSetting } from "@/modules/site-builder/utils/class-style"
import { cn } from "@/utils"
//-----------------------------------------------------------------------------------------------
interface IProps {
  selected: boolean
  data: I_Section
  isLiveView?: boolean
  onClick?: () => void
  colors: string[]
}
//-----------------------------------------------------------------------------------------------
export default function FAQPreviewLayout({
  selected,
  data,
  isLiveView,
  onClick,
  colors,
}: IProps) {
  const fields = (
    typeof data.fields === "string" ? JSON.parse(data.fields) : data.fields
  ) as T_FAQFields

  const { palette, padding_bottom, padding_top, setting_layout, layout } =
    fields

  const styleByPalette = getStyleByPalette(palette, colors)
  const styleHeight = getStyleHeightSetting(setting_layout as T_SettingLayout)

  return (
    <BlockPreview
      selected={selected}
      isLiveView={isLiveView}
      labelSelected="Edit FAQ"
    >
      <div
        onClick={onClick}
        className={cn("faq-layout flex w-full text-center")}
        style={{
          paddingTop: padding_top ? `${padding_top}px` : undefined,
          paddingBottom: padding_bottom ? `${padding_bottom}px` : undefined,
          ...styleByPalette,
          ...styleHeight,
        }}
      >
        {layout === "layout-1" && <div>1</div>}
        {layout === "layout-2" && <div>1</div>}
        {layout === "layout-3" && <div>1</div>}
        {layout === "layout-4" && <div>1</div>}
        {layout === "layout-5" && <div>1</div>}
      </div>
    </BlockPreview>
  )
}
